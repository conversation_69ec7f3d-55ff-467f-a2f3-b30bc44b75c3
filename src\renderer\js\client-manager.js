/**
 * Client management functionality
 * Reason for Function: Handles client CRUD operations, modal interactions, and client data management
 * Task Performed: Manages client creation, editing, deletion, and display in table format
 * Linking Information:
 *   - Internal Link: Uses Supabase MCP tools for database operations
 *   - Internal Link: Interacts with client elements in src/renderer/pages/clients.html
 *   - Internal Link: Updates client table and manages client data
 */

class ClientManager {
  constructor() {
    this.clients = [];
    this.currentEditingClient = null;
    this.init();
  }

  /**
   * Initialize client manager
   * Reason for Function: Sets up event listeners and loads existing clients
   * Task Performed: Binds modal events, form handlers, and loads client data
   * Linking Information:
   *   - Internal Link: Called when clients page is loaded
   */
  init() {
    this.bindEvents();
    this.loadClients();
    this.setDefaultDate();
  }

  /**
   * Bind client-related events
   * Reason for Function: Attaches event listeners to client management elements
   * Task Performed: Sets up modal, form, and client interaction handlers
   * Linking Information:
   *   - Internal Link: Binds to elements in clients.html
   */
  bindEvents() {
    // Add client button
    const addClientBtn = document.getElementById('add-client-btn');
    if (addClientBtn) {
      addClientBtn.addEventListener('click', () => this.openClientModal());
    }

    // Modal close buttons
    const closeModal = document.getElementById('close-client-modal');
    const cancelClient = document.getElementById('cancel-client');
    const modalOverlay = document.querySelector('#client-modal .modal-overlay');

    if (closeModal) {
      closeModal.addEventListener('click', () => this.closeClientModal());
    }
    if (cancelClient) {
      cancelClient.addEventListener('click', () => this.closeClientModal());
    }
    if (modalOverlay) {
      modalOverlay.addEventListener('click', () => this.closeClientModal());
    }

    // Form submission
    const clientForm = document.getElementById('client-form');
    if (clientForm) {
      clientForm.addEventListener('submit', (e) => this.handleClientSubmission(e));
    }

    // Delete modal events
    const closeDeleteModal = document.getElementById('close-delete-modal');
    const cancelDelete = document.getElementById('cancel-delete');
    const confirmDelete = document.getElementById('confirm-delete');
    const deleteModalOverlay = document.querySelector('#delete-client-modal .modal-overlay');

    if (closeDeleteModal) {
      closeDeleteModal.addEventListener('click', () => this.closeDeleteModal());
    }
    if (cancelDelete) {
      cancelDelete.addEventListener('click', () => this.closeDeleteModal());
    }
    if (deleteModalOverlay) {
      deleteModalOverlay.addEventListener('click', () => this.closeDeleteModal());
    }
    if (confirmDelete) {
      confirmDelete.addEventListener('click', () => this.confirmDeleteClient());
    }
  }

  /**
   * Set default date to today
   * Reason for Function: Sets the date added field to current date by default
   * Task Performed: Sets default value for date input field
   * Linking Information:
   *   - Internal Link: Called during initialization
   */
  setDefaultDate() {
    const dateInput = document.getElementById('date-added');
    if (dateInput) {
      const today = new Date().toISOString().split('T')[0];
      dateInput.value = today;
    }
  }

  /**
   * Open client modal for adding or editing
   * Reason for Function: Shows the client creation/editing modal
   * Task Performed: Displays modal, sets title, clears or populates form
   * Linking Information:
   *   - Internal Link: Called by add client button or edit client action
   */
  openClientModal(client = null) {
    const modal = document.getElementById('client-modal');
    const title = document.getElementById('client-modal-title');
    const saveBtn = document.getElementById('save-client');
    
    if (modal && title && saveBtn) {
      this.currentEditingClient = client;
      
      if (client) {
        title.textContent = 'Edit Client';
        saveBtn.textContent = 'Update Client';
        this.populateForm(client);
      } else {
        title.textContent = 'Add New Client';
        saveBtn.textContent = 'Save Client';
        this.resetForm();
      }
      
      modal.classList.remove('hidden');
      
      // Focus on first input
      const firstInput = document.getElementById('client-name');
      if (firstInput) {
        setTimeout(() => firstInput.focus(), 100);
      }
    }
  }

  /**
   * Close client modal
   * Reason for Function: Hides the client creation/editing modal
   * Task Performed: Adds hidden class to modal and resets state
   * Linking Information:
   *   - Internal Link: Called by close buttons and overlay click
   */
  closeClientModal() {
    const modal = document.getElementById('client-modal');
    if (modal) {
      modal.classList.add('hidden');
      this.currentEditingClient = null;
    }
  }

  /**
   * Reset client form
   * Reason for Function: Clears all form fields and resets to default state
   * Task Performed: Clears inputs and sets default date
   * Linking Information:
   *   - Internal Link: Called when opening modal for new client
   */
  resetForm() {
    const form = document.getElementById('client-form');
    if (form) {
      form.reset();
      this.setDefaultDate();
    }
  }

  /**
   * Populate form with client data
   * Reason for Function: Fills form fields with existing client data for editing
   * Task Performed: Sets form field values from client object
   * Linking Information:
   *   - Internal Link: Called when editing existing client
   */
  populateForm(client) {
    document.getElementById('client-name').value = client.client_name || '';
    document.getElementById('business-name').value = client.business_name || '';
    document.getElementById('business-website').value = client.business_website || '';
    document.getElementById('telephone-number').value = client.telephone_number || '';
    document.getElementById('email-address').value = client.email_address || '';
    document.getElementById('client-location').value = client.client_location || '';
    document.getElementById('date-added').value = client.date_added || '';
  }

  /**
   * Handle client form submission
   * Reason for Function: Processes client creation/editing form and saves to database
   * Task Performed: Validates form, creates/updates client object, saves to database
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async handleClientSubmission(event) {
    event.preventDefault();
    
    const clientData = {
      client_name: document.getElementById('client-name').value.trim(),
      business_name: document.getElementById('business-name').value.trim(),
      business_website: document.getElementById('business-website').value.trim(),
      telephone_number: document.getElementById('telephone-number').value.trim(),
      email_address: document.getElementById('email-address').value.trim(),
      client_location: document.getElementById('client-location').value.trim(),
      date_added: document.getElementById('date-added').value
    };

    // Validation
    if (!clientData.client_name || !clientData.business_name || !clientData.telephone_number || 
        !clientData.email_address || !clientData.client_location || !clientData.date_added) {
      alert('Please fill in all required fields.');
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(clientData.email_address)) {
      alert('Please enter a valid email address.');
      return;
    }

    try {
      const saveBtn = document.getElementById('save-client');
      this.setLoading(saveBtn, true);

      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      clientData.user_id = currentUser.id;

      if (this.currentEditingClient) {
        await this.updateClient(this.currentEditingClient.id, clientData);
      } else {
        await this.createClient(clientData);
      }

      this.closeClientModal();
      this.loadClients();
      
    } catch (error) {
      console.error('Error saving client:', error);
      alert('Failed to save client. Please try again.');
    } finally {
      const saveBtn = document.getElementById('save-client');
      this.setLoading(saveBtn, false);
    }
  }

  /**
   * Create client in database using MCP tools
   * Reason for Function: Saves new client data to Supabase database
   * Task Performed: Inserts new client record with all client information
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async createClient(clientData) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    const { data, error } = await client
      .from('clients')
      .insert([{
        client_name: clientData.client_name,
        business_name: clientData.business_name,
        business_website: clientData.business_website || null,
        telephone_number: clientData.telephone_number,
        email_address: clientData.email_address,
        client_location: clientData.client_location,
        date_added: clientData.date_added,
        user_id: clientData.user_id
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Update client in database using MCP tools
   * Reason for Function: Updates existing client data in Supabase database
   * Task Performed: Updates client record with modified information
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async updateClient(clientId, clientData) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    const { data, error } = await client
      .from('clients')
      .update({
        client_name: clientData.client_name,
        business_name: clientData.business_name,
        business_website: clientData.business_website || null,
        telephone_number: clientData.telephone_number,
        email_address: clientData.email_address,
        client_location: clientData.client_location,
        date_added: clientData.date_added,
        updated_at: new Date().toISOString()
      })
      .eq('id', clientId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  /**
   * Load clients from database using MCP tools
   * Reason for Function: Retrieves and displays all user clients
   * Task Performed: Fetches clients from database and updates table display
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadClients() {
    try {
      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();
      
      if (!currentUser) return;

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { data, error } = await client
        .from('clients')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      this.clients = data || [];
      this.renderClientsTable();
      
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  }

  /**
   * Render clients table
   * Reason for Function: Displays clients in a responsive table format
   * Task Performed: Creates table rows for each client with action buttons
   * Linking Information:
   *   - Internal Link: Updates table elements in clients.html
   */
  renderClientsTable() {
    const tableBody = document.getElementById('clients-table-body');
    const emptyState = document.getElementById('clients-empty-state');
    const tableContainer = document.querySelector('.clients-table-container .table-wrapper');

    if (!tableBody || !emptyState || !tableContainer) return;

    if (this.clients.length === 0) {
      tableContainer.style.display = 'none';
      emptyState.classList.remove('hidden');
      return;
    }

    tableContainer.style.display = 'block';
    emptyState.classList.add('hidden');

    tableBody.innerHTML = '';

    this.clients.forEach(client => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${client.client_name}</td>
        <td>${client.business_name}</td>
        <td>${client.email_address}</td>
        <td>${client.telephone_number}</td>
        <td>${client.client_location}</td>
        <td>${new Date(client.date_added).toLocaleDateString()}</td>
        <td class="actions-cell">
          <button class="btn-icon edit-btn" onclick="clientManager.editClient('${client.id}')" title="Edit Client">
            ✏️
          </button>
          <button class="btn-icon delete-btn" onclick="clientManager.deleteClient('${client.id}', '${client.client_name}')" title="Delete Client">
            🗑️
          </button>
        </td>
      `;
      tableBody.appendChild(row);
    });
  }

  /**
   * Edit client
   * Reason for Function: Opens modal with client data for editing
   * Task Performed: Finds client by ID and opens edit modal
   * Linking Information:
   *   - Internal Link: Called by edit button in table
   */
  editClient(clientId) {
    const client = this.clients.find(c => c.id === clientId);
    if (client) {
      this.openClientModal(client);
    }
  }

  /**
   * Delete client
   * Reason for Function: Shows delete confirmation modal
   * Task Performed: Opens delete confirmation modal with client details
   * Linking Information:
   *   - Internal Link: Called by delete button in table
   */
  deleteClient(clientId, clientName) {
    this.clientToDelete = clientId;
    const modal = document.getElementById('delete-client-modal');
    const nameElement = document.getElementById('delete-client-name');
    
    if (modal && nameElement) {
      nameElement.textContent = clientName;
      modal.classList.remove('hidden');
    }
  }

  /**
   * Close delete modal
   * Reason for Function: Hides the delete confirmation modal
   * Task Performed: Adds hidden class to delete modal
   * Linking Information:
   *   - Internal Link: Called by cancel and close buttons
   */
  closeDeleteModal() {
    const modal = document.getElementById('delete-client-modal');
    if (modal) {
      modal.classList.add('hidden');
      this.clientToDelete = null;
    }
  }

  /**
   * Confirm delete client
   * Reason for Function: Deletes client from database after confirmation
   * Task Performed: Removes client from database using MCP tools
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async confirmDeleteClient() {
    if (!this.clientToDelete) return;

    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { error } = await client
        .from('clients')
        .delete()
        .eq('id', this.clientToDelete);

      if (error) throw error;

      this.closeDeleteModal();
      this.loadClients();
      
    } catch (error) {
      console.error('Error deleting client:', error);
      alert('Failed to delete client. Please try again.');
    }
  }

  /**
   * Set loading state for buttons
   * Reason for Function: Shows loading state during async operations
   * Task Performed: Adds/removes loading class and disables button
   * Linking Information:
   *   - Internal Link: Used during client creation and updates
   */
  setLoading(button, loading) {
    if (loading) {
      button.classList.add('loading');
      button.disabled = true;
    } else {
      button.classList.remove('loading');
      button.disabled = false;
    }
  }
}

// Export for global use
window.ClientManager = ClientManager;
