/**
 * Invoice management functionality
 * Reason for Function: Handles invoice CRUD operations, PDF generation, and invoice data management
 * Task Performed: Manages invoice creation, editing, deletion, and PDF generation with jsPDF
 * Linking Information:
 *   - Internal Link: Uses Supabase MCP tools for database operations
 *   - Internal Link: Interacts with invoice elements in src/renderer/pages/invoice.html
 *   - External Link: Uses jsPDF library for PDF generation
 */

class InvoiceManager {
  constructor() {
    this.invoices = [];
    this.clients = [];
    this.projects = [];
    this.businessDetails = null;
    this.currentEditingInvoice = null;
    this.currentInvoiceItems = [];
    this.init();
  }

  /**
   * Initialize invoice manager
   * Reason for Function: Sets up event listeners and loads initial data
   * Task Performed: Binds UI events and loads invoices, clients, and projects data
   * Linking Information:
   *   - Internal Link: Called on class instantiation
   */
  init() {
    this.bindEvents();
    this.loadInvoiceData();
  }

  /**
   * Bind event listeners
   * Reason for Function: Attaches event handlers to invoice management UI elements
   * Task Performed: Sets up click handlers for buttons and form submissions
   * Linking Information:
   *   - Internal Link: Binds to elements in invoice.html
   */
  bindEvents() {
    // Create invoice button
    const createInvoiceBtn = document.getElementById('create-invoice-btn');
    if (createInvoiceBtn) {
      createInvoiceBtn.addEventListener('click', () => this.openInvoiceModal());
    }

    // Modal close buttons
    const closeInvoiceModal = document.getElementById('close-invoice-modal');
    if (closeInvoiceModal) {
      closeInvoiceModal.addEventListener('click', () => this.closeInvoiceModal());
    }

    // Cancel button
    const cancelInvoice = document.getElementById('cancel-invoice');
    if (cancelInvoice) {
      cancelInvoice.addEventListener('click', () => this.closeInvoiceModal());
    }

    // Form submission
    const invoiceForm = document.getElementById('invoice-form');
    if (invoiceForm) {
      invoiceForm.addEventListener('submit', (e) => this.handleInvoiceSubmit(e));
    }

    // Add item button
    const addItemBtn = document.getElementById('add-invoice-item');
    if (addItemBtn) {
      addItemBtn.addEventListener('click', () => this.addInvoiceItem());
    }

    // Set default invoice date to today
    const invoiceDate = document.getElementById('invoice-date');
    if (invoiceDate) {
      invoiceDate.value = new Date().toISOString().split('T')[0];
    }

    // Set default due date to 30 days from today
    const dueDate = document.getElementById('invoice-due-date');
    if (dueDate) {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      dueDate.value = futureDate.toISOString().split('T')[0];
    }
  }

  /**
   * Load invoice data
   * Reason for Function: Loads invoices, clients, projects, and business details from database
   * Task Performed: Fetches all necessary data for invoice management
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadInvoiceData() {
    try {
      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();

      if (!currentUser) return;

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Load invoices
      const { data: invoicesData, error: invoicesError } = await client
        .from('invoices')
        .select(`
          *,
          clients (
            client_name,
            business_name,
            email_address
          )
        `)
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (invoicesError) throw invoicesError;

      // Load clients
      const { data: clientsData, error: clientsError } = await client
        .from('clients')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('client_name', { ascending: true });

      if (clientsError) throw clientsError;

      // Load projects
      const { data: projectsData, error: projectsError } = await client
        .from('projects')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('project_name', { ascending: true });

      if (projectsError) throw projectsError;

      // Load business details
      const { data: businessData, error: businessError } = await client
        .from('business_details')
        .select('*')
        .eq('user_id', currentUser.id)
        .single();

      if (businessError && businessError.code !== 'PGRST116') {
        throw businessError;
      }

      this.invoices = invoicesData || [];
      this.clients = clientsData || [];
      this.projects = projectsData || [];
      this.businessDetails = businessData || null;

      this.renderInvoicesTable();
      this.populateSelects();

    } catch (error) {
      console.error('Error loading invoice data:', error);
    }
  }

  /**
   * Render invoices table
   * Reason for Function: Displays invoices in table format
   * Task Performed: Creates table rows with invoice information
   * Linking Information:
   *   - Internal Link: Updates table elements in invoice.html
   */
  renderInvoicesTable() {
    const tableContainer = document.getElementById('invoices-table-container');
    const tableBody = document.getElementById('invoices-table-body');
    const emptyState = document.getElementById('invoices-empty-state');

    if (!tableContainer || !tableBody || !emptyState) return;

    if (this.invoices.length === 0) {
      tableContainer.style.display = 'none';
      emptyState.classList.remove('hidden');
      return;
    }

    tableContainer.style.display = 'block';
    emptyState.classList.add('hidden');

    tableBody.innerHTML = '';

    this.invoices.forEach(invoice => {
      const row = document.createElement('tr');
      const clientName = invoice.clients ? invoice.clients.client_name : 'No Client';
      const invoiceDate = new Date(invoice.invoice_date).toLocaleDateString();
      const dueDate = invoice.due_date ? new Date(invoice.due_date).toLocaleDateString() : 'N/A';

      row.innerHTML = `
        <td>${invoice.invoice_number}</td>
        <td>${clientName}</td>
        <td>${invoiceDate}</td>
        <td>${dueDate}</td>
        <td>$${invoice.total_amount.toFixed(2)}</td>
        <td>
          <span class="status-badge invoice-${invoice.status}">
            ${this.getInvoiceStatusLabel(invoice.status)}
          </span>
        </td>
        <td class="actions-cell">
          <button class="btn btn-sm btn-primary" onclick="invoiceManager.generatePDF('${invoice.id}')" title="Generate PDF">
            📄 PDF
          </button>
          <button class="btn btn-sm btn-secondary" onclick="invoiceManager.editInvoice('${invoice.id}')" title="Edit Invoice">
            ✏️ Edit
          </button>
          <button class="btn btn-sm btn-danger" onclick="invoiceManager.deleteInvoice('${invoice.id}', '${invoice.invoice_number}')" title="Delete Invoice">
            🗑️ Delete
          </button>
        </td>
      `;
      tableBody.appendChild(row);
    });
  }

  /**
   * Get invoice status label
   * Reason for Function: Converts invoice status code to human-readable label
   * Task Performed: Returns formatted invoice status text
   * Linking Information:
   *   - Internal Link: Used by renderInvoicesTable for status display
   */
  getInvoiceStatusLabel(status) {
    switch (status) {
      case 'draft': return 'Draft';
      case 'sent': return 'Sent';
      case 'paid': return 'Paid';
      case 'overdue': return 'Overdue';
      case 'cancelled': return 'Cancelled';
      default: return 'Unknown';
    }
  }

  /**
   * Populate select dropdowns
   * Reason for Function: Fills client and project selection dropdowns
   * Task Performed: Populates select elements with available options
   * Linking Information:
   *   - Internal Link: Updates select elements in invoice modal
   */
  populateSelects() {
    const clientSelect = document.getElementById('invoice-client-select');
    const projectSelect = document.getElementById('invoice-project-select');
    
    if (clientSelect) {
      clientSelect.innerHTML = '<option value="">Select a client</option>';
      
      this.clients.forEach(client => {
        const option = document.createElement('option');
        option.value = client.id;
        option.textContent = `${client.client_name} (${client.business_name})`;
        clientSelect.appendChild(option);
      });
    }

    if (projectSelect) {
      projectSelect.innerHTML = '<option value="">Select a project (optional)</option>';
      
      this.projects.forEach(project => {
        const option = document.createElement('option');
        option.value = project.id;
        option.textContent = project.project_name;
        projectSelect.appendChild(option);
      });
    }
  }

  /**
   * Open invoice modal
   * Reason for Function: Shows the create/edit invoice modal
   * Task Performed: Displays modal for creating or editing invoices
   * Linking Information:
   *   - Internal Link: Called by create invoice button
   */
  openInvoiceModal(invoice = null) {
    const modal = document.getElementById('invoice-modal');
    const title = document.getElementById('invoice-modal-title');
    const saveBtn = document.getElementById('save-invoice');
    
    if (modal && title && saveBtn) {
      this.currentEditingInvoice = invoice;
      
      if (invoice) {
        title.textContent = 'Edit Invoice';
        saveBtn.textContent = 'Update Invoice';
        this.populateInvoiceForm(invoice);
      } else {
        title.textContent = 'Create New Invoice';
        saveBtn.textContent = 'Create Invoice';
        this.resetInvoiceForm();
      }
      
      modal.classList.remove('hidden');

      // Populate business details form
      this.populateBusinessDetailsForm();

      // Focus on first input
      const firstInput = document.getElementById('invoice-number');
      if (firstInput) {
        setTimeout(() => firstInput.focus(), 100);
      }
    }
  }

  /**
   * Close invoice modal
   * Reason for Function: Hides the invoice modal
   * Task Performed: Adds hidden class to invoice modal
   * Linking Information:
   *   - Internal Link: Called by cancel and close buttons
   */
  closeInvoiceModal() {
    const modal = document.getElementById('invoice-modal');
    if (modal) {
      modal.classList.add('hidden');
      this.resetInvoiceForm();
      this.currentEditingInvoice = null;
    }
  }

  /**
   * Reset invoice form
   * Reason for Function: Clears all invoice form fields
   * Task Performed: Resets form inputs to default values
   * Linking Information:
   *   - Internal Link: Called when opening/closing invoice modal
   */
  resetInvoiceForm() {
    const form = document.getElementById('invoice-form');
    if (form) {
      form.reset();
      
      // Set default dates
      const invoiceDate = document.getElementById('invoice-date');
      const dueDate = document.getElementById('invoice-due-date');
      
      if (invoiceDate) {
        invoiceDate.value = new Date().toISOString().split('T')[0];
      }
      
      if (dueDate) {
        const futureDate = new Date();
        futureDate.setDate(futureDate.getDate() + 30);
        dueDate.value = futureDate.toISOString().split('T')[0];
      }

      // Generate new invoice number
      this.generateInvoiceNumber();
      
      // Reset invoice items and add one default item
      this.currentInvoiceItems = [];
      this.addInvoiceItem(); // Add one default item

      // Populate business details if available
      this.populateBusinessDetailsForm();
    }
  }

  /**
   * Generate invoice number
   * Reason for Function: Creates unique invoice number for new invoices
   * Task Performed: Generates sequential invoice number based on existing invoices
   * Linking Information:
   *   - Internal Link: Called when creating new invoices
   */
  generateInvoiceNumber() {
    const invoiceNumberInput = document.getElementById('invoice-number');
    if (invoiceNumberInput && !this.currentEditingInvoice) {
      const year = new Date().getFullYear();
      const nextNumber = this.invoices.length + 1;
      const invoiceNumber = `INV-${year}-${nextNumber.toString().padStart(4, '0')}`;
      invoiceNumberInput.value = invoiceNumber;
    }
  }

  /**
   * Add invoice item
   * Reason for Function: Adds new item to current invoice
   * Task Performed: Creates new invoice item and updates display
   * Linking Information:
   *   - Internal Link: Called by add item button
   */
  addInvoiceItem(parentItemId = null) {
    const newItem = {
      id: Date.now().toString(), // Temporary ID
      parent_item_id: parentItemId,
      item_name: '',
      description: '',
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      position: this.currentInvoiceItems.length
    };

    this.currentInvoiceItems.push(newItem);
    this.renderInvoiceItems();
  }

  /**
   * Remove invoice item
   * Reason for Function: Removes item from current invoice
   * Task Performed: Deletes invoice item and updates display
   * Linking Information:
   *   - Internal Link: Called by remove item button
   */
  removeInvoiceItem(itemId) {
    this.currentInvoiceItems = this.currentInvoiceItems.filter(item =>
      item.id !== itemId && item.parent_item_id !== itemId
    );
    this.renderInvoiceItems();
    this.calculateInvoiceTotal();
  }

  /**
   * Render invoice items
   * Reason for Function: Displays current invoice items in the form
   * Task Performed: Creates HTML for invoice items with input fields
   * Linking Information:
   *   - Internal Link: Updates invoice items container in modal
   */
  renderInvoiceItems() {
    const container = document.getElementById('invoice-items-container');
    if (!container) return;

    container.innerHTML = '';

    this.currentInvoiceItems.forEach((item, index) => {
      const isSubItem = item.parent_item_id !== null;
      const itemDiv = document.createElement('div');
      itemDiv.className = `invoice-item ${isSubItem ? 'sub-item' : 'main-item'}`;
      itemDiv.dataset.itemId = item.id;

      itemDiv.innerHTML = `
        <div class="item-row">
          <div class="item-field">
            <label>Item Name *</label>
            <input type="text" class="item-name" value="${item.item_name}"
                   onchange="invoiceManager.updateItemField('${item.id}', 'item_name', this.value)" required>
          </div>
          <div class="item-field">
            <label>Description</label>
            <input type="text" class="item-description" value="${item.description}"
                   onchange="invoiceManager.updateItemField('${item.id}', 'description', this.value)">
          </div>
          <div class="item-field">
            <label>Qty</label>
            <input type="number" class="item-quantity" value="${item.quantity}" min="1" step="0.01"
                   onchange="invoiceManager.updateItemField('${item.id}', 'quantity', this.value)">
          </div>
          <div class="item-field">
            <label>Unit Price</label>
            <input type="number" class="item-unit-price" value="${item.unit_price}" min="0" step="0.01"
                   onchange="invoiceManager.updateItemField('${item.id}', 'unit_price', this.value)">
          </div>
          <div class="item-field">
            <label>Total</label>
            <input type="number" class="item-total" value="${item.total_price}" readonly>
          </div>
          <div class="item-actions">
            ${!isSubItem ? `
              <button type="button" class="btn btn-sm btn-secondary" onclick="invoiceManager.addInvoiceItem('${item.id}')">
                ➕ Sub
              </button>
            ` : ''}
            <button type="button" class="btn btn-sm btn-danger" onclick="invoiceManager.removeInvoiceItem('${item.id}')">
              🗑️
            </button>
          </div>
        </div>
      `;

      container.appendChild(itemDiv);
    });

    // Add "Add Item" button
    const addButton = document.createElement('button');
    addButton.type = 'button';
    addButton.className = 'btn btn-secondary add-item-btn';
    addButton.innerHTML = '➕ Add Item';
    addButton.onclick = () => this.addInvoiceItem();
    container.appendChild(addButton);
  }

  /**
   * Update item field
   * Reason for Function: Updates specific field of an invoice item
   * Task Performed: Updates item data and recalculates totals
   * Linking Information:
   *   - Internal Link: Called by item input change events
   */
  updateItemField(itemId, field, value) {
    const item = this.currentInvoiceItems.find(i => i.id === itemId);
    if (!item) return;

    item[field] = field === 'quantity' || field === 'unit_price' ? parseFloat(value) || 0 : value;

    // Calculate total price for this item
    if (field === 'quantity' || field === 'unit_price') {
      item.total_price = item.quantity * item.unit_price;

      // Update the total input field
      const totalInput = document.querySelector(`[data-item-id="${itemId}"] .item-total`);
      if (totalInput) {
        totalInput.value = item.total_price.toFixed(2);
      }

      // If this is a main item with sub-items, calculate based on sub-items
      const subItems = this.currentInvoiceItems.filter(i => i.parent_item_id === itemId);
      if (subItems.length > 0) {
        item.total_price = subItems.reduce((sum, subItem) => sum + subItem.total_price, 0);
        item.unit_price = item.quantity > 0 ? item.total_price / item.quantity : 0;

        // Update the unit price and total inputs
        const unitPriceInput = document.querySelector(`[data-item-id="${itemId}"] .item-unit-price`);
        if (unitPriceInput) {
          unitPriceInput.value = item.unit_price.toFixed(2);
        }
        if (totalInput) {
          totalInput.value = item.total_price.toFixed(2);
        }
      }

      this.calculateInvoiceTotal();
    }
  }

  /**
   * Calculate invoice total
   * Reason for Function: Calculates and updates invoice subtotal, tax, and total
   * Task Performed: Computes invoice totals and updates display
   * Linking Information:
   *   - Internal Link: Updates total fields in invoice form
   */
  calculateInvoiceTotal() {
    const mainItems = this.currentInvoiceItems.filter(item => !item.parent_item_id);
    const subtotal = mainItems.reduce((sum, item) => sum + item.total_price, 0);

    const taxRate = parseFloat(document.getElementById('invoice-tax-rate')?.value || 0);
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    // Update form fields
    const subtotalInput = document.getElementById('invoice-subtotal');
    const taxAmountInput = document.getElementById('invoice-tax-amount');
    const totalInput = document.getElementById('invoice-total');

    if (subtotalInput) subtotalInput.value = subtotal.toFixed(2);
    if (taxAmountInput) taxAmountInput.value = taxAmount.toFixed(2);
    if (totalInput) totalInput.value = total.toFixed(2);
  }

  /**
   * Handle invoice form submission
   * Reason for Function: Processes invoice creation or update
   * Task Performed: Validates form data and saves invoice to database
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async handleInvoiceSubmit(e) {
    e.preventDefault();

    // Validation
    if (this.currentInvoiceItems.length === 0) {
      alert('Please add at least one item to the invoice.');
      return;
    }

    const invoiceData = {
      invoice_number: document.getElementById('invoice-number').value.trim(),
      client_id: document.getElementById('invoice-client-select').value,
      project_id: document.getElementById('invoice-project-select').value || null,
      invoice_date: document.getElementById('invoice-date').value,
      due_date: document.getElementById('invoice-due-date').value || null,
      status: document.getElementById('invoice-status').value || 'draft',
      subtotal: parseFloat(document.getElementById('invoice-subtotal').value) || 0,
      tax_rate: parseFloat(document.getElementById('invoice-tax-rate').value) || 0,
      tax_amount: parseFloat(document.getElementById('invoice-tax-amount').value) || 0,
      total_amount: parseFloat(document.getElementById('invoice-total').value) || 0,
      notes: document.getElementById('invoice-notes').value.trim(),
      terms_conditions: document.getElementById('invoice-terms').value.trim()
    };

    // Collect business details
    const businessData = {
      business_name: document.getElementById('business-name').value.trim(),
      business_phone: document.getElementById('business-phone').value.trim(),
      business_address: document.getElementById('business-address').value.trim(),
      business_email: document.getElementById('business-email').value.trim(),
      business_website: document.getElementById('business-website').value.trim(),
      tax_number: document.getElementById('tax-number').value.trim(),
      bank_name: document.getElementById('bank-name').value.trim(),
      bank_account_number: document.getElementById('account-number').value.trim(),
      bank_routing_number: document.getElementById('routing-number').value.trim()
    };

    if (!invoiceData.invoice_number || !invoiceData.client_id || !businessData.business_name) {
      alert('Please fill in all required fields including business name.');
      return;
    }

    try {
      const saveBtn = document.getElementById('save-invoice');
      this.setLoading(saveBtn, true);

      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();

      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      invoiceData.user_id = currentUser.id;

      // Save business details first
      await this.saveBusinessDetails(businessData, currentUser.id);

      if (this.currentEditingInvoice) {
        await this.updateInvoice(this.currentEditingInvoice.id, invoiceData);
      } else {
        await this.createInvoice(invoiceData);
      }

      this.closeInvoiceModal();
      this.loadInvoiceData();

    } catch (error) {
      console.error('Error saving invoice:', error);
      alert('Failed to save invoice. Please try again.');
    } finally {
      const saveBtn = document.getElementById('save-invoice');
      this.setLoading(saveBtn, false);
    }
  }

  /**
   * Create invoice in database
   * Reason for Function: Saves new invoice data to Supabase database
   * Task Performed: Inserts new invoice record and associated items
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async createInvoice(invoiceData) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    // Create invoice
    const { data: invoiceResult, error: invoiceError } = await client
      .from('invoices')
      .insert([invoiceData])
      .select()
      .single();

    if (invoiceError) throw invoiceError;

    // Create invoice items
    if (this.currentInvoiceItems.length > 0) {
      const itemsData = this.currentInvoiceItems.map((item, index) => ({
        invoice_id: invoiceResult.id,
        parent_item_id: item.parent_item_id === null ? null :
          this.currentInvoiceItems.find(i => i.id === item.parent_item_id)?.temp_db_id || null,
        item_name: item.item_name,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        position: index,
        user_id: invoiceData.user_id
      }));

      const { error: itemsError } = await client
        .from('invoice_items')
        .insert(itemsData);

      if (itemsError) throw itemsError;
    }

    return invoiceResult;
  }

  /**
   * Update invoice in database
   * Reason for Function: Updates existing invoice data in Supabase database
   * Task Performed: Updates invoice record and manages items
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async updateInvoice(invoiceId, invoiceData) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    // Update invoice
    const { data: invoiceResult, error: invoiceError } = await client
      .from('invoices')
      .update({
        ...invoiceData,
        updated_at: new Date().toISOString()
      })
      .eq('id', invoiceId)
      .select()
      .single();

    if (invoiceError) throw invoiceError;

    // Delete existing items and create new ones
    await client
      .from('invoice_items')
      .delete()
      .eq('invoice_id', invoiceId);

    if (this.currentInvoiceItems.length > 0) {
      const itemsData = this.currentInvoiceItems.map((item, index) => ({
        invoice_id: invoiceId,
        parent_item_id: item.parent_item_id === null ? null :
          this.currentInvoiceItems.find(i => i.id === item.parent_item_id)?.temp_db_id || null,
        item_name: item.item_name,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        position: index,
        user_id: invoiceData.user_id
      }));

      const { error: itemsError } = await client
        .from('invoice_items')
        .insert(itemsData);

      if (itemsError) throw itemsError;
    }

    return invoiceResult;
  }

  /**
   * Generate PDF invoice
   * Reason for Function: Creates PDF version of invoice using jsPDF
   * Task Performed: Generates and downloads PDF invoice with professional layout
   * Linking Information:
   *   - External Link: Uses jsPDF library for PDF generation
   */
  async generatePDF(invoiceId) {
    try {
      console.log('Starting PDF generation for invoice:', invoiceId);

      const invoice = this.invoices.find(inv => inv.id === invoiceId);
      if (!invoice) {
        alert('Invoice not found');
        return;
      }

      console.log('Found invoice:', invoice);

      // Load invoice items
      const client = window.getSupabaseClient();
      const { data: items, error } = await client
        .from('invoice_items')
        .select('*')
        .eq('invoice_id', invoiceId)
        .order('position', { ascending: true });

      if (error) throw error;

      console.log('Loaded invoice items:', items);

      // Import jsPDF with multiple fallback options
      let jsPDF;
      if (window.jspdf && window.jspdf.jsPDF) {
        jsPDF = window.jspdf.jsPDF;
      } else if (window.jsPDF) {
        jsPDF = window.jsPDF;
      } else if (typeof window.jspdf === 'function') {
        jsPDF = window.jspdf;
      } else {
        throw new Error('jsPDF library not found. Please check if the library is loaded correctly.');
      }

      console.log('jsPDF constructor:', jsPDF);
      const doc = new jsPDF();

      // Set up fonts and colors
      doc.setFont('helvetica');

      // Header
      doc.setFontSize(24);
      doc.setTextColor(44, 62, 80);
      doc.text('INVOICE', 20, 30);

      // Business details (if available)
      let businessYPos = 50;
      if (this.businessDetails) {
        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text(this.businessDetails.business_name || 'Your Business', 20, businessYPos);
        businessYPos += 10;

        if (this.businessDetails.business_address) {
          doc.text(this.businessDetails.business_address, 20, businessYPos);
          businessYPos += 10;
        }
        if (this.businessDetails.business_phone) {
          doc.text(`Phone: ${this.businessDetails.business_phone}`, 20, businessYPos);
          businessYPos += 10;
        }
        if (this.businessDetails.business_email) {
          doc.text(`Email: ${this.businessDetails.business_email}`, 20, businessYPos);
          businessYPos += 10;
        }
        if (this.businessDetails.business_website) {
          doc.text(`Website: ${this.businessDetails.business_website}`, 20, businessYPos);
          businessYPos += 10;
        }
        if (this.businessDetails.tax_number) {
          doc.text(`Tax Number: ${this.businessDetails.tax_number}`, 20, businessYPos);
          businessYPos += 10;
        }
      }

      // Invoice details
      doc.setFontSize(10);
      doc.text(`Invoice #: ${invoice.invoice_number}`, 140, 50);
      doc.text(`Date: ${new Date(invoice.invoice_date).toLocaleDateString()}`, 140, 60);
      if (invoice.due_date) {
        doc.text(`Due Date: ${new Date(invoice.due_date).toLocaleDateString()}`, 140, 70);
      }

      // Client details
      doc.setFontSize(12);
      doc.setTextColor(44, 62, 80);
      doc.text('Bill To:', 20, 100);
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(10);

      const client_info = invoice.clients;
      if (client_info) {
        doc.text(client_info.client_name, 20, 110);
        if (client_info.business_name) {
          doc.text(client_info.business_name, 20, 120);
        }
        if (client_info.email_address) {
          doc.text(client_info.email_address, 20, 130);
        }
      }

      // Items table
      let yPos = 150;

      // Table headers
      doc.setFillColor(44, 62, 80);
      doc.rect(20, yPos, 170, 10, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(9);
      doc.text('Description', 25, yPos + 7);
      doc.text('Qty', 120, yPos + 7);
      doc.text('Price', 140, yPos + 7);
      doc.text('Total', 165, yPos + 7);

      yPos += 15;
      doc.setTextColor(0, 0, 0);

      // Table rows
      const mainItems = items.filter(item => !item.parent_item_id);
      mainItems.forEach(item => {
        doc.text(item.item_name, 25, yPos);
        if (item.description) {
          doc.setFontSize(8);
          doc.setTextColor(100, 100, 100);
          doc.text(item.description, 25, yPos + 5);
          doc.setFontSize(9);
          doc.setTextColor(0, 0, 0);
          yPos += 5;
        }

        doc.text(item.quantity.toString(), 120, yPos);
        doc.text(`$${item.unit_price.toFixed(2)}`, 140, yPos);
        doc.text(`$${item.total_price.toFixed(2)}`, 165, yPos);

        yPos += 10;

        // Sub-items
        const subItems = items.filter(subItem => subItem.parent_item_id === item.id);
        subItems.forEach(subItem => {
          doc.setFontSize(8);
          doc.setTextColor(100, 100, 100);
          doc.text(`  • ${subItem.item_name}`, 30, yPos);
          doc.text(subItem.quantity.toString(), 120, yPos);
          doc.text(`$${subItem.unit_price.toFixed(2)}`, 140, yPos);
          doc.text(`$${subItem.total_price.toFixed(2)}`, 165, yPos);
          yPos += 8;
          doc.setFontSize(9);
          doc.setTextColor(0, 0, 0);
        });
      });

      // Totals
      yPos += 10;
      doc.line(120, yPos, 190, yPos);
      yPos += 10;

      doc.text('Subtotal:', 140, yPos);
      doc.text(`$${invoice.subtotal.toFixed(2)}`, 165, yPos);
      yPos += 10;

      if (invoice.tax_rate > 0) {
        doc.text(`Tax (${invoice.tax_rate}%):`, 140, yPos);
        doc.text(`$${invoice.tax_amount.toFixed(2)}`, 165, yPos);
        yPos += 10;
      }

      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.text('Total:', 140, yPos);
      doc.text(`$${invoice.total_amount.toFixed(2)}`, 165, yPos);

      // Notes and terms
      if (invoice.notes) {
        yPos += 20;
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');
        doc.text('Notes:', 20, yPos);
        doc.text(invoice.notes, 20, yPos + 10);
      }

      if (invoice.terms_conditions) {
        yPos += 30;
        doc.setFontSize(10);
        doc.text('Terms & Conditions:', 20, yPos);
        doc.text(invoice.terms_conditions, 20, yPos + 10);
      }

      // Payment details
      if (this.businessDetails && (this.businessDetails.bank_name || this.businessDetails.bank_account_number)) {
        yPos += 30;
        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        doc.text('Payment Details:', 20, yPos);
        yPos += 10;

        doc.setFont('helvetica', 'normal');
        if (this.businessDetails.bank_name) {
          doc.text(`Bank: ${this.businessDetails.bank_name}`, 20, yPos);
          yPos += 8;
        }
        if (this.businessDetails.bank_account_number) {
          doc.text(`Account Number: ${this.businessDetails.bank_account_number}`, 20, yPos);
          yPos += 8;
        }
        if (this.businessDetails.bank_routing_number) {
          doc.text(`Routing/SWIFT: ${this.businessDetails.bank_routing_number}`, 20, yPos);
          yPos += 8;
        }
      }

      // Save the PDF
      doc.save(`Invoice-${invoice.invoice_number}.pdf`);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  }

  /**
   * Populate invoice form with existing data
   * Reason for Function: Fills form fields when editing an existing invoice
   * Task Performed: Loads invoice data into form fields for editing
   * Linking Information:
   *   - Internal Link: Called when editing invoices
   */
  async populateInvoiceForm(invoice) {
    // Populate basic fields
    document.getElementById('invoice-number').value = invoice.invoice_number || '';
    document.getElementById('invoice-client-select').value = invoice.client_id || '';
    document.getElementById('invoice-project-select').value = invoice.project_id || '';
    document.getElementById('invoice-date').value = invoice.invoice_date || '';
    document.getElementById('invoice-due-date').value = invoice.due_date || '';
    document.getElementById('invoice-status').value = invoice.status || 'draft';
    document.getElementById('invoice-tax-rate').value = invoice.tax_rate || 0;
    document.getElementById('invoice-notes').value = invoice.notes || '';
    document.getElementById('invoice-terms').value = invoice.terms_conditions || '';

    // Load invoice items
    try {
      const client = window.getSupabaseClient();
      const { data: items, error } = await client
        .from('invoice_items')
        .select('*')
        .eq('invoice_id', invoice.id)
        .order('position', { ascending: true });

      if (error) throw error;

      this.currentInvoiceItems = items.map(item => ({
        id: item.id,
        parent_item_id: item.parent_item_id,
        item_name: item.item_name,
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        position: item.position
      }));

      this.renderInvoiceItems();
      this.calculateInvoiceTotal();

    } catch (error) {
      console.error('Error loading invoice items:', error);
      this.currentInvoiceItems = [];
    }
  }

  /**
   * Edit invoice
   * Reason for Function: Opens modal with invoice data for editing
   * Task Performed: Finds invoice by ID and opens edit modal
   * Linking Information:
   *   - Internal Link: Called by edit button in invoice table
   */
  editInvoice(invoiceId) {
    const invoice = this.invoices.find(inv => inv.id === invoiceId);
    if (invoice) {
      this.openInvoiceModal(invoice);
    }
  }

  /**
   * Delete invoice
   * Reason for Function: Shows delete confirmation and removes invoice
   * Task Performed: Confirms deletion and removes invoice from database
   * Linking Information:
   *   - Internal Link: Called by delete button in invoice table
   */
  async deleteInvoice(invoiceId, invoiceNumber) {
    if (!confirm(`Are you sure you want to delete invoice ${invoiceNumber}? This action cannot be undone.`)) {
      return;
    }

    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { error } = await client
        .from('invoices')
        .delete()
        .eq('id', invoiceId);

      if (error) throw error;

      // Refresh the invoices display
      this.loadInvoiceData();

    } catch (error) {
      console.error('Error deleting invoice:', error);
      alert('Failed to delete invoice. Please try again.');
    }
  }

  /**
   * Save business details
   * Reason for Function: Saves or updates business details for the user
   * Task Performed: Upserts business details in the database
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async saveBusinessDetails(businessData, userId) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    businessData.user_id = userId;

    // Try to update existing business details first
    const { data: existingBusiness, error: fetchError } = await client
      .from('business_details')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingBusiness) {
      // Update existing
      const { error: updateError } = await client
        .from('business_details')
        .update({
          ...businessData,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) throw updateError;
    } else {
      // Create new
      const { error: insertError } = await client
        .from('business_details')
        .insert([businessData]);

      if (insertError) throw insertError;
    }

    // Update local business details
    this.businessDetails = businessData;
  }

  /**
   * Populate business details form
   * Reason for Function: Fills business details form with existing data
   * Task Performed: Loads business details into form fields
   * Linking Information:
   *   - Internal Link: Called when opening invoice modal
   */
  populateBusinessDetailsForm() {
    if (this.businessDetails) {
      document.getElementById('business-name').value = this.businessDetails.business_name || '';
      document.getElementById('business-phone').value = this.businessDetails.business_phone || '';
      document.getElementById('business-address').value = this.businessDetails.business_address || '';
      document.getElementById('business-email').value = this.businessDetails.business_email || '';
      document.getElementById('business-website').value = this.businessDetails.business_website || '';
      document.getElementById('tax-number').value = this.businessDetails.tax_number || '';
      document.getElementById('bank-name').value = this.businessDetails.bank_name || '';
      document.getElementById('account-number').value = this.businessDetails.bank_account_number || '';
      document.getElementById('routing-number').value = this.businessDetails.bank_routing_number || '';
    }
  }

  /**
   * Set loading state for buttons
   * Reason for Function: Shows loading state during async operations
   * Task Performed: Adds/removes loading class and disables button
   * Linking Information:
   *   - Internal Link: Used during invoice operations
   */
  setLoading(button, loading) {
    if (loading) {
      button.classList.add('loading');
      button.disabled = true;
    } else {
      button.classList.remove('loading');
      button.disabled = false;
    }
  }
}

// Export for global use
window.InvoiceManager = InvoiceManager;
