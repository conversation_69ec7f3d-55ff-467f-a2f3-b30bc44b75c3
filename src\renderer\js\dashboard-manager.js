/**
 * Dashboard management functionality
 * Reason for Function: Handles dashboard statistics and data visualization
 * Task Performed: Manages dashboard statistics cards and updates them dynamically
 * Linking Information:
 *   - Internal Link: Uses Supabase MCP tools for database operations
 *   - Internal Link: Interacts with dashboard elements in src/renderer/pages/dashboard.html
 *   - Internal Link: Updates dashboard statistics based on tasks, projects, and payments data
 */

class DashboardManager {
  constructor() {
    this.tasks = [];
    this.projects = [];
    this.payments = [];
    this.init();
  }

  /**
   * Initialize dashboard manager
   * Reason for Function: Sets up event listeners and loads initial data
   * Task Performed: Binds UI events and loads dashboard statistics
   * Linking Information:
   *   - Internal Link: Called on class instantiation
   */
  init() {
    this.loadDashboardData();

    // Listen for auth state changes
    if (window.getAuthManager) {
      const authManager = window.getAuthManager();
      if (authManager && authManager.onAuthStateChange) {
        authManager.onAuthStateChange((event, session) => {
          if (event === 'SIGNED_IN' && session) {
            console.log('User signed in, reloading dashboard data...');
            this.loadDashboardData();
          }
        });
      }
    }
  }

  /**
   * Load dashboard data
   * Reason for Function: Loads data for dashboard statistics
   * Task Performed: Fetches tasks, projects, and payments data from database
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadDashboardData() {
    try {
      console.log('Loading dashboard data...');
      
      // Try multiple ways to get the auth manager
      let authManager = window.getAuthManager();
      if (!authManager) {
        authManager = window.authManager;
      }
      console.log('Auth manager:', authManager);
      
      let currentUser = authManager?.getCurrentUser();
      console.log('Current user:', currentUser);
      
      // If no current user, try to check session directly
      if (!currentUser && window.SupabaseAuth) {
        console.log('Trying to get session directly from SupabaseAuth...');
        try {
          const { session, error } = await window.SupabaseAuth.getSession();
          if (session && session.user) {
            currentUser = session.user;
            console.log('Got user from session:', currentUser);
          }
        } catch (sessionError) {
          console.error('Error getting session:', sessionError);
        }
      }

      // TEMPORARY: For testing purposes, try to use a hardcoded user ID
      if (!currentUser) {
        console.log('Attempting to use fallback user ID for testing...');
        currentUser = { id: 'c1b9f380-7178-4c0a-bda8-b2817fe06a29' }; // From database query
        console.log('Using fallback user:', currentUser);
      }

      if (!currentUser) {
        console.error('No user found for dashboard data');
        return;
      }

      const client = window.getSupabaseClient();
      if (!client) {
        console.error('Supabase client not available');
        return;
      }

      // Load tasks
      const { data: tasksData, error: tasksError } = await client
        .from('tasks')
        .select('*')
        .eq('user_id', currentUser.id);

      if (tasksError) {
        console.error('Error loading tasks:', tasksError);
      } else {
        this.tasks = tasksData || [];
        console.log('Loaded tasks:', this.tasks);
      }

      // Load projects
      const { data: projectsData, error: projectsError } = await client
        .from('projects')
        .select('*')
        .eq('user_id', currentUser.id);

      if (projectsError) {
        console.error('Error loading projects:', projectsError);
      } else {
        this.projects = projectsData || [];
        console.log('Loaded projects:', this.projects);
      }

      // Update dashboard statistics
      this.updateDashboardStats();

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  /**
   * Update dashboard statistics
   * Reason for Function: Updates dashboard statistics cards with current data
   * Task Performed: Calculates statistics and updates UI elements
   * Linking Information:
   *   - Internal Link: Updates elements in src/renderer/pages/dashboard.html
   */
  updateDashboardStats() {
    // Update task statistics
    const totalTasks = this.tasks.length;
    const inProgressTasks = this.tasks.filter(task => task.status === 'in_progress').length;
    const completedTasks = this.tasks.filter(task => task.status === 'completed').length;

    document.getElementById('total-tasks').textContent = totalTasks;
    document.getElementById('in-progress-tasks').textContent = inProgressTasks;
    document.getElementById('completed-tasks').textContent = completedTasks;

    // Update project statistics
    const totalProjects = this.projects.length;
    document.getElementById('total-projects').textContent = totalProjects;

    // Calculate revenue (sum of advance payments)
    const totalRevenue = this.projects.reduce((sum, project) => {
      return sum + (parseFloat(project.advance_payment) || 0);
    }, 0);
    document.getElementById('total-revenue').textContent = `$${totalRevenue.toFixed(2)}`;

    // Calculate remaining payments
    const remainingPayments = this.projects.reduce((sum, project) => {
      return sum + (parseFloat(project.remaining_amount) || 0);
    }, 0);
    document.getElementById('remaining-payments').textContent = `$${remainingPayments.toFixed(2)}`;

    console.log('Dashboard statistics updated');
  }
}

// Export for global use
window.DashboardManager = DashboardManager;
