/**
 * Payment Management System
 * Reason for Function: Manages payment records, statistics, and CRUD operations
 * Task Performed: Handles payment data loading, filtering, and management using MCP tools
 * Linking Information:
 *   - Internal Link: Uses Supabase MCP tools for database operations
 *   - Internal Link: Interacts with payment elements in src/renderer/pages/payments.html
 *   - Internal Link: Manages payment data from payments table
 */

console.log('🔄 Payment manager script loading...');
console.log('📍 Script location: src/renderer/js/payment-manager.js');
console.log('🌐 Window object available:', typeof window !== 'undefined');
console.log('📄 Document object available:', typeof document !== 'undefined');

// Test if basic JavaScript is working
try {
  console.log('✅ JavaScript execution test passed');
  window.paymentManagerScriptLoaded = true;
  console.log('✅ paymentManagerScriptLoaded flag set to true');
} catch (error) {
  console.error('❌ JavaScript execution test failed:', error);
}

class PaymentSystemManager {
  constructor() {
    console.log('🔄 PaymentSystemManager constructor called');
    try {
      this.payments = [];
      this.projects = [];
      this.filteredPayments = [];
      this.currentUser = null;
      this.initialized = false;
      console.log('✅ PaymentSystemManager properties initialized');

      // Initialize asynchronously to avoid blocking constructor
      this.init().catch(error => {
        console.error('❌ Error during async initialization:', error);
      });
    } catch (error) {
      console.error('❌ Error in PaymentSystemManager constructor:', error);
      throw error;
    }
  }

  /**
   * Initialize payment system
   * Reason for Function: Sets up event listeners and loads initial data
   * Task Performed: Binds UI events and loads payment data from database
   * Linking Information:
   *   - Internal Link: Called on class instantiation
   */
  async init() {
    console.log('🔄 PaymentSystemManager initializing...');
    try {
      console.log('🔄 Binding events...');
      this.bindEvents();
      console.log('🔄 Loading payment data...');
      await this.loadPaymentData();
      this.initialized = true;
      console.log('✅ PaymentSystemManager initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing PaymentSystemManager:', error);
      this.initialized = false;
      // Don't throw here to prevent breaking the entire app
    }
  }

  /**
   * Check if payment system is ready
   * Reason for Function: Verifies that the payment system has been fully initialized
   * Task Performed: Returns initialization status for external checks
   * Linking Information:
   *   - Internal Link: Used by navigation.js to verify system readiness
   */
  isReady() {
    return this.initialized === true;
  }

  /**
   * Bind event listeners
   * Reason for Function: Attaches event handlers to payment UI elements
   * Task Performed: Sets up click handlers for buttons, forms, and filters
   * Linking Information:
   *   - Internal Link: Binds to elements in payments.html
   */
  bindEvents() {
    console.log('Binding payment events...');

    // Add payment button
    const addPaymentBtn = document.getElementById('add-payment-btn');
    if (addPaymentBtn) {
      addPaymentBtn.addEventListener('click', () => this.openAddPaymentModal());
    }

    // Refresh button
    const refreshBtn = document.getElementById('refresh-payments');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadPaymentData());
    }

    // Modal close buttons
    const closePaymentModal = document.getElementById('close-payment-modal');
    const cancelPayment = document.getElementById('cancel-payment');
    if (closePaymentModal) closePaymentModal.addEventListener('click', () => this.closeAddPaymentModal());
    if (cancelPayment) cancelPayment.addEventListener('click', () => this.closeAddPaymentModal());

    const closeDetailsModal = document.getElementById('close-details-modal');
    const closePaymentDetails = document.getElementById('close-payment-details');
    if (closeDetailsModal) closeDetailsModal.addEventListener('click', () => this.closeDetailsModal());
    if (closePaymentDetails) closePaymentDetails.addEventListener('click', () => this.closeDetailsModal());

    // Payment form submission
    const paymentForm = document.getElementById('payment-form');
    if (paymentForm) {
      paymentForm.addEventListener('submit', (e) => this.handlePaymentSubmit(e));
    }

    // Filter controls
    const filterType = document.getElementById('filter-type');
    const filterStatus = document.getElementById('filter-status');
    const filterMethod = document.getElementById('filter-method');
    const clearFilters = document.getElementById('clear-filters');

    if (filterType) filterType.addEventListener('change', () => this.applyFilters());
    if (filterStatus) filterStatus.addEventListener('change', () => this.applyFilters());
    if (filterMethod) filterMethod.addEventListener('change', () => this.applyFilters());
    if (clearFilters) clearFilters.addEventListener('click', () => this.clearFilters());

    // Set default payment date to today
    const paymentDate = document.getElementById('payment-date');
    if (paymentDate) {
      paymentDate.value = new Date().toISOString().split('T')[0];
    }

    console.log('Payment events bound successfully');
  }

  /**
   * Load payment data from database
   * Reason for Function: Fetches payment records and related data using MCP tools
   * Task Performed: Loads payments with project and client information
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadPaymentData() {
    console.log('Loading payment data...');
    this.showLoading(true);

    try {
      // Get current user
      this.currentUser = await this.getCurrentUser();
      if (!this.currentUser) {
        console.error('No authenticated user found');
        this.showEmptyState('Please log in to view payments');
        return;
      }

      console.log('Loading data for user:', this.currentUser.id);

      const client = window.getSupabaseClient();
      if (!client) {
        throw new Error('Supabase client not available');
      }

      // Load payments with project and client information
      const { data: paymentsData, error: paymentsError } = await client
        .from('payments')
        .select(`
          *,
          projects (
            project_name,
            total_project_cost,
            clients (
              client_name,
              business_name
            )
          )
        `)
        .eq('user_id', this.currentUser.id)
        .order('created_at', { ascending: false });

      if (paymentsError) {
        throw paymentsError;
      }

      this.payments = paymentsData || [];
      this.filteredPayments = [...this.payments];

      console.log('Loaded payments:', this.payments);

      // Load projects for the add payment form
      const { data: projectsData, error: projectsError } = await client
        .from('projects')
        .select('id, project_name, total_project_cost')
        .eq('user_id', this.currentUser.id)
        .order('project_name');

      if (projectsError) {
        console.error('Error loading projects:', projectsError);
      } else {
        this.projects = projectsData || [];
        this.populateProjectSelect();
      }

      this.updateStatistics();
      this.renderPaymentTable();

    } catch (error) {
      console.error('Error loading payment data:', error);
      this.showEmptyState('Error loading payments. Please try again.');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Get current authenticated user
   * Reason for Function: Retrieves the current user for data filtering
   * Task Performed: Gets user from auth manager or uses fallback
   * Linking Information:
   *   - Internal Link: Uses auth manager for user authentication
   */
  async getCurrentUser() {
    // Try to get user from auth manager
    let authManager = window.getAuthManager();
    if (!authManager) {
      authManager = window.authManager;
    }

    let currentUser = authManager?.getCurrentUser();

    // If no current user, try to check session directly
    if (!currentUser && window.SupabaseAuth) {
      try {
        const { session, error } = await window.SupabaseAuth.getSession();
        if (session && session.user) {
          currentUser = session.user;
        }
      } catch (sessionError) {
        console.error('Error getting session:', sessionError);
      }
    }

    // TEMPORARY: For testing purposes, use fallback user ID
    if (!currentUser) {
      console.log('Using fallback user ID for testing...');
      currentUser = { id: 'c1b9f380-7178-4c0a-bda8-b2817fe06a29' };
    }

    return currentUser;
  }

  /**
   * Update payment statistics
   * Reason for Function: Calculates and displays payment statistics
   * Task Performed: Updates statistics cards with current payment data
   * Linking Information:
   *   - Internal Link: Updates elements in payments.html
   */
  updateStatistics() {
    const totalPayments = this.payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const confirmedPayments = this.payments
      .filter(payment => payment.is_confirmed)
      .reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const pendingPayments = this.payments
      .filter(payment => !payment.is_confirmed)
      .reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const paymentCount = this.payments.length;

    document.getElementById('total-payments').textContent = `$${totalPayments.toFixed(2)}`;
    document.getElementById('confirmed-payments').textContent = `$${confirmedPayments.toFixed(2)}`;
    document.getElementById('pending-payments').textContent = `$${pendingPayments.toFixed(2)}`;
    document.getElementById('payment-count').textContent = paymentCount;

    console.log('Statistics updated:', { totalPayments, confirmedPayments, pendingPayments, paymentCount });
  }

  /**
   * Render payment table
   * Reason for Function: Displays payment data in table format
   * Task Performed: Creates table rows with payment information and action buttons
   * Linking Information:
   *   - Internal Link: Updates table in payments.html
   */
  renderPaymentTable() {
    const tableBody = document.getElementById('payments-table-body');
    const tableContainer = document.getElementById('payments-table-container');
    const emptyState = document.getElementById('payments-empty-state');

    if (!tableBody || !tableContainer || !emptyState) {
      console.error('Required table elements not found');
      return;
    }

    if (this.filteredPayments.length === 0) {
      tableContainer.style.display = 'none';
      emptyState.classList.remove('hidden');
      return;
    }

    tableContainer.style.display = 'block';
    emptyState.classList.add('hidden');

    tableBody.innerHTML = '';

    this.filteredPayments.forEach(payment => {
      const row = document.createElement('tr');
      
      const projectName = payment.projects?.project_name || 'Unknown Project';
      const clientName = payment.projects?.clients?.client_name || 'No Client';
      const businessName = payment.projects?.clients?.business_name || '';
      const paymentDate = new Date(payment.payment_date).toLocaleDateString();
      const statusClass = payment.is_confirmed ? 'confirmed' : 'pending';
      const statusText = payment.is_confirmed ? '✅ Confirmed' : '⏳ Pending';

      row.innerHTML = `
        <td>
          <div class="payment-project">
            <strong>${projectName}</strong>
            <small class="text-muted">${clientName}${businessName ? ` (${businessName})` : ''}</small>
          </div>
        </td>
        <td>
          <span class="payment-type-badge type-${payment.payment_type}">
            ${this.getPaymentTypeLabel(payment.payment_type)}
          </span>
        </td>
        <td class="amount-cell">$${parseFloat(payment.amount).toFixed(2)}</td>
        <td>${paymentDate}</td>
        <td>${payment.payment_method || 'Not specified'}</td>
        <td>
          <span class="status-badge status-${statusClass}">
            ${statusText}
          </span>
        </td>
        <td class="actions-cell">
          <button class="btn btn-sm btn-secondary" onclick="paymentSystem.viewPaymentDetails('${payment.id}')" title="View Details">
            👁️ View
          </button>
          ${!payment.is_confirmed ? `
            <button class="btn btn-sm btn-success" onclick="paymentSystem.confirmPayment('${payment.id}')" title="Confirm Payment">
              ✅ Confirm
            </button>
          ` : ''}
        </td>
      `;

      tableBody.appendChild(row);
    });

    console.log('Payment table rendered with', this.filteredPayments.length, 'payments');
  }

  /**
   * Get payment type label
   * Reason for Function: Converts payment type code to human-readable label
   * Task Performed: Returns formatted payment type text
   * Linking Information:
   *   - Internal Link: Used by renderPaymentTable for display
   */
  getPaymentTypeLabel(paymentType) {
    const labels = {
      'advance': 'Advance Payment',
      'remaining': 'Remaining Payment',
      'full': 'Full Payment',
      'partial': 'Partial Payment'
    };
    return labels[paymentType] || 'Unknown Type';
  }

  /**
   * Show/hide loading state
   * Reason for Function: Provides visual feedback during data loading
   * Task Performed: Shows or hides loading spinner
   * Linking Information:
   *   - Internal Link: Controls loading element in payments.html
   */
  showLoading(show) {
    const loadingElement = document.getElementById('payments-loading');
    const tableContainer = document.getElementById('payments-table-container');
    const emptyState = document.getElementById('payments-empty-state');

    if (show) {
      if (loadingElement) loadingElement.classList.remove('hidden');
      if (tableContainer) tableContainer.style.display = 'none';
      if (emptyState) emptyState.classList.add('hidden');
    } else {
      if (loadingElement) loadingElement.classList.add('hidden');
    }
  }

  /**
   * Show empty state with message
   * Reason for Function: Displays empty state when no data is available
   * Task Performed: Shows empty state with custom message
   * Linking Information:
   *   - Internal Link: Controls empty state element in payments.html
   */
  showEmptyState(message = 'No payment records found') {
    const emptyState = document.getElementById('payments-empty-state');
    const tableContainer = document.getElementById('payments-table-container');
    
    if (emptyState) {
      emptyState.querySelector('p').textContent = message;
      emptyState.classList.remove('hidden');
    }
    if (tableContainer) {
      tableContainer.style.display = 'none';
    }
  }
}

  /**
   * Open add payment modal
   * Reason for Function: Opens modal for adding new payment
   * Task Performed: Shows modal and populates project dropdown
   * Linking Information:
   *   - Internal Link: Shows modal defined in payments.html
   */
  openAddPaymentModal() {
    const modal = document.getElementById('add-payment-modal');
    if (modal) {
      modal.classList.remove('hidden');
      this.populateProjectSelect();
    }
  }

  /**
   * Close add payment modal
   * Reason for Function: Closes add payment modal and resets form
   * Task Performed: Hides modal and clears form data
   * Linking Information:
   *   - Internal Link: Hides modal defined in payments.html
   */
  closeAddPaymentModal() {
    const modal = document.getElementById('add-payment-modal');
    const form = document.getElementById('payment-form');
    if (modal) modal.classList.add('hidden');
    if (form) form.reset();
  }

  /**
   * Close payment details modal
   * Reason for Function: Closes payment details modal
   * Task Performed: Hides details modal
   * Linking Information:
   *   - Internal Link: Hides modal defined in payments.html
   */
  closeDetailsModal() {
    const modal = document.getElementById('payment-details-modal');
    if (modal) modal.classList.add('hidden');
  }

  /**
   * Populate project select dropdown
   * Reason for Function: Fills project dropdown with available projects
   * Task Performed: Populates select element with project options
   * Linking Information:
   *   - Internal Link: Updates select element in payments.html
   */
  populateProjectSelect() {
    const projectSelect = document.getElementById('payment-project');
    if (!projectSelect) return;

    projectSelect.innerHTML = '<option value="">Select Project</option>';

    this.projects.forEach(project => {
      const option = document.createElement('option');
      option.value = project.id;
      option.textContent = `${project.project_name} ($${project.total_project_cost})`;
      projectSelect.appendChild(option);
    });
  }

  /**
   * Handle payment form submission
   * Reason for Function: Processes new payment creation
   * Task Performed: Validates form data and creates payment record
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async handlePaymentSubmit(e) {
    e.preventDefault();

    try {
      const formData = {
        project_id: document.getElementById('payment-project').value,
        payment_type: document.getElementById('payment-type').value,
        amount: parseFloat(document.getElementById('payment-amount').value),
        payment_date: document.getElementById('payment-date').value,
        payment_method: document.getElementById('payment-method').value,
        is_confirmed: document.getElementById('payment-confirmed').value === 'true',
        notes: document.getElementById('payment-notes').value.trim(),
        user_id: this.currentUser.id
      };

      // Validate required fields
      if (!formData.project_id || !formData.payment_type || !formData.amount) {
        alert('Please fill in all required fields');
        return;
      }

      const client = window.getSupabaseClient();
      if (!client) {
        throw new Error('Database client not available');
      }

      const { error } = await client
        .from('payments')
        .insert([formData]);

      if (error) throw error;

      console.log('Payment created successfully');
      this.closeAddPaymentModal();
      await this.loadPaymentData();

    } catch (error) {
      console.error('Error creating payment:', error);
      alert('Failed to create payment. Please try again.');
    }
  }

  /**
   * View payment details
   * Reason for Function: Shows detailed payment information
   * Task Performed: Displays payment details in modal
   * Linking Information:
   *   - Internal Link: Shows details modal defined in payments.html
   */
  viewPaymentDetails(paymentId) {
    const payment = this.payments.find(p => p.id === paymentId);
    if (!payment) {
      alert('Payment not found');
      return;
    }

    const modal = document.getElementById('payment-details-modal');
    const content = modal.querySelector('.payment-details-content');

    if (!content) return;

    const projectName = payment.projects?.project_name || 'Unknown Project';
    const clientName = payment.projects?.clients?.client_name || 'No Client';
    const businessName = payment.projects?.clients?.business_name || '';
    const paymentDate = new Date(payment.payment_date).toLocaleDateString();
    const createdDate = new Date(payment.created_at).toLocaleDateString();

    content.innerHTML = `
      <div class="payment-detail-grid">
        <div class="detail-item">
          <label>Project:</label>
          <span>${projectName}</span>
        </div>
        <div class="detail-item">
          <label>Client:</label>
          <span>${clientName}${businessName ? ` (${businessName})` : ''}</span>
        </div>
        <div class="detail-item">
          <label>Payment Type:</label>
          <span>${this.getPaymentTypeLabel(payment.payment_type)}</span>
        </div>
        <div class="detail-item">
          <label>Amount:</label>
          <span class="amount">$${parseFloat(payment.amount).toFixed(2)}</span>
        </div>
        <div class="detail-item">
          <label>Payment Date:</label>
          <span>${paymentDate}</span>
        </div>
        <div class="detail-item">
          <label>Payment Method:</label>
          <span>${payment.payment_method || 'Not specified'}</span>
        </div>
        <div class="detail-item">
          <label>Status:</label>
          <span class="status-badge status-${payment.is_confirmed ? 'confirmed' : 'pending'}">
            ${payment.is_confirmed ? '✅ Confirmed' : '⏳ Pending'}
          </span>
        </div>
        <div class="detail-item">
          <label>Created:</label>
          <span>${createdDate}</span>
        </div>
        ${payment.notes ? `
          <div class="detail-item full-width">
            <label>Notes:</label>
            <span>${payment.notes}</span>
          </div>
        ` : ''}
      </div>
    `;

    modal.classList.remove('hidden');
  }

  /**
   * Confirm payment
   * Reason for Function: Marks payment as confirmed
   * Task Performed: Updates payment confirmation status
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async confirmPayment(paymentId) {
    try {
      const client = window.getSupabaseClient();
      if (!client) {
        throw new Error('Database client not available');
      }

      const { error } = await client
        .from('payments')
        .update({
          is_confirmed: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId);

      if (error) throw error;

      console.log('Payment confirmed successfully');
      await this.loadPaymentData();

    } catch (error) {
      console.error('Error confirming payment:', error);
      alert('Failed to confirm payment. Please try again.');
    }
  }

  /**
   * Apply filters to payment list
   * Reason for Function: Filters payments based on user selection
   * Task Performed: Filters payment array and re-renders table
   * Linking Information:
   *   - Internal Link: Uses filter controls from payments.html
   */
  applyFilters() {
    const typeFilter = document.getElementById('filter-type').value;
    const statusFilter = document.getElementById('filter-status').value;
    const methodFilter = document.getElementById('filter-method').value;

    this.filteredPayments = this.payments.filter(payment => {
      const typeMatch = !typeFilter || payment.payment_type === typeFilter;
      const statusMatch = !statusFilter ||
        (statusFilter === 'confirmed' && payment.is_confirmed) ||
        (statusFilter === 'pending' && !payment.is_confirmed);
      const methodMatch = !methodFilter || payment.payment_method === methodFilter;

      return typeMatch && statusMatch && methodMatch;
    });

    this.renderPaymentTable();
    console.log('Filters applied:', { typeFilter, statusFilter, methodFilter, resultCount: this.filteredPayments.length });
  }

  /**
   * Clear all filters
   * Reason for Function: Resets all filter controls and shows all payments
   * Task Performed: Clears filter selections and resets display
   * Linking Information:
   *   - Internal Link: Resets filter controls in payments.html
   */
  clearFilters() {
    document.getElementById('filter-type').value = '';
    document.getElementById('filter-status').value = '';
    document.getElementById('filter-method').value = '';

    this.filteredPayments = [...this.payments];
    this.renderPaymentTable();
    console.log('Filters cleared');
  }
}

// Export for global use
console.log('✅ PaymentSystemManager class defined');
console.log('🔄 Exporting PaymentSystemManager to window object...');
try {
  window.PaymentSystemManager = PaymentSystemManager;
  console.log('✅ PaymentSystemManager exported successfully');
  console.log('🔍 window.PaymentSystemManager type:', typeof window.PaymentSystemManager);
} catch (error) {
  console.error('❌ Error exporting PaymentSystemManager:', error);
}

// Create a simple initialization function
window.initializePaymentSystem = function() {
  console.log('🔄 initializePaymentSystem called');
  try {
    if (!window.paymentSystem) {
      console.log('🔄 Creating new PaymentSystemManager instance...');
      window.paymentSystem = new PaymentSystemManager();
      console.log('✅ PaymentSystemManager instance created via function');

      // Give it a moment to initialize
      setTimeout(() => {
        if (window.paymentSystem && window.paymentSystem.isReady) {
          if (window.paymentSystem.isReady()) {
            console.log('✅ PaymentSystemManager fully initialized and ready');
          } else {
            console.log('⏳ PaymentSystemManager still initializing...');
          }
        }
      }, 1000);

      return true;
    } else {
      console.log('✅ PaymentSystemManager instance already exists');
      if (window.paymentSystem.isReady && window.paymentSystem.isReady()) {
        console.log('✅ Existing PaymentSystemManager is ready');
      } else {
        console.log('⏳ Existing PaymentSystemManager still initializing...');
      }
      return true;
    }
  } catch (error) {
    console.error('❌ Error in initializePaymentSystem:', error);
    return false;
  }
};

// Try to create instance automatically, but don't fail if it doesn't work
try {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🔄 Auto-creating PaymentSystemManager instance (DOM ready)...');
      window.initializePaymentSystem();
    });
  } else {
    // DOM is already ready
    console.log('🔄 Auto-creating PaymentSystemManager instance (DOM already ready)...');
    window.initializePaymentSystem();
  }
} catch (error) {
  console.error('❌ Error in auto-initialization:', error);
  console.log('💡 PaymentSystemManager can still be initialized manually via initializePaymentSystem()');
}
