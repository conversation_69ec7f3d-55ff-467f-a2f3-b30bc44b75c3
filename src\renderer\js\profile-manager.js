/**
 * Profile management functionality
 * Reason for Function: Handles user profile data loading, editing, and statistics
 * Task Performed: Manages profile information display and updates
 * Linking Information:
 *   - Internal Link: Uses Supabase MCP tools for database operations
 *   - Internal Link: Interacts with profile elements in src/renderer/pages/profile.html
 *   - Internal Link: Updates profile information and user statistics
 */

class ProfileManager {
  constructor() {
    this.currentUser = null;
    this.profileData = null;
    this.init();
  }

  /**
   * Initialize profile manager
   * Reason for Function: Sets up event listeners and loads profile data
   * Task Performed: Binds UI events and loads user profile information
   * Linking Information:
   *   - Internal Link: Called on class instantiation
   */
  init() {
    this.bindEvents();
    this.loadProfileData();
  }

  /**
   * Bind profile events
   * Reason for Function: Attaches event listeners to profile UI elements
   * Task Performed: Sets up edit profile modal and form submission handlers
   * Linking Information:
   *   - Internal Link: Binds to elements in src/renderer/pages/profile.html
   */
  bindEvents() {
    // Edit profile button
    const editBtn = document.getElementById('edit-profile-btn');
    if (editBtn) {
      editBtn.addEventListener('click', () => this.openEditModal());
    }

    // Close modal buttons
    const closeBtn = document.getElementById('close-edit-profile');
    const cancelBtn = document.getElementById('cancel-edit-profile');
    if (closeBtn) closeBtn.addEventListener('click', () => this.closeEditModal());
    if (cancelBtn) cancelBtn.addEventListener('click', () => this.closeEditModal());

    // Edit profile form
    const editForm = document.getElementById('edit-profile-form');
    if (editForm) {
      editForm.addEventListener('submit', (e) => this.handleProfileUpdate(e));
    }

    // Close modal on backdrop click
    const modal = document.getElementById('edit-profile-modal');
    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.closeEditModal();
        }
      });
    }
  }

  /**
   * Load profile data
   * Reason for Function: Loads user profile and statistics data
   * Task Performed: Fetches profile data from database and updates UI
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadProfileData() {
    try {
      console.log('Loading profile data...');
      
      // Get current user
      let authManager = window.getAuthManager();
      if (!authManager) {
        authManager = window.authManager;
      }
      
      let currentUser = authManager?.getCurrentUser();
      
      // If no current user, try to check session directly
      if (!currentUser && window.SupabaseAuth) {
        try {
          const { session, error } = await window.SupabaseAuth.getSession();
          if (session && session.user) {
            currentUser = session.user;
          }
        } catch (sessionError) {
          console.error('Error getting session:', sessionError);
        }
      }

      // TEMPORARY: For testing purposes, try to use a hardcoded user ID
      if (!currentUser) {
        currentUser = { 
          id: 'c1b9f380-7178-4c0a-bda8-b2817fe06a29',
          email: '<EMAIL>'
        };
      }

      if (!currentUser) {
        console.error('No user found for profile data');
        return;
      }

      this.currentUser = currentUser;

      const client = window.getSupabaseClient();
      if (!client) {
        console.error('Supabase client not available');
        return;
      }

      // Load profile data
      const { data: profileData, error: profileError } = await client
        .from('profiles')
        .select('*')
        .eq('id', currentUser.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error loading profile:', profileError);
      } else {
        this.profileData = profileData || {};
        console.log('Loaded profile:', this.profileData);
      }

      // Load user statistics
      await this.loadUserStatistics();

      // Update profile display
      this.updateProfileDisplay();

    } catch (error) {
      console.error('Error loading profile data:', error);
    }
  }

  /**
   * Load user statistics
   * Reason for Function: Loads user statistics for profile display
   * Task Performed: Fetches counts of tasks, projects, clients, and invoices
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadUserStatistics() {
    try {
      const client = window.getSupabaseClient();
      if (!client || !this.currentUser) return;

      // Load tasks count
      const { count: tasksCount } = await client
        .from('tasks')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', this.currentUser.id);

      // Load projects count
      const { count: projectsCount } = await client
        .from('projects')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', this.currentUser.id);

      // Load clients count
      const { count: clientsCount } = await client
        .from('clients')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', this.currentUser.id);

      // Load invoices count
      const { count: invoicesCount } = await client
        .from('invoices')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', this.currentUser.id);

      // Update statistics display
      document.getElementById('user-total-tasks').textContent = tasksCount || 0;
      document.getElementById('user-total-projects').textContent = projectsCount || 0;
      document.getElementById('user-total-clients').textContent = clientsCount || 0;
      document.getElementById('user-total-invoices').textContent = invoicesCount || 0;

    } catch (error) {
      console.error('Error loading user statistics:', error);
    }
  }

  /**
   * Update profile display
   * Reason for Function: Updates the profile UI with loaded data
   * Task Performed: Updates profile information display elements
   * Linking Information:
   *   - Internal Link: Updates elements in src/renderer/pages/profile.html
   */
  updateProfileDisplay() {
    if (!this.currentUser) return;

    // Update profile name
    const fullName = this.profileData?.full_name || 'User';
    document.getElementById('profile-name').textContent = fullName;

    // Update profile email
    const email = this.currentUser.email || 'No email';
    document.getElementById('profile-email').textContent = email;
    document.getElementById('detail-email').textContent = email;

    // Update joined date
    const joinedDate = new Date(this.currentUser.created_at || Date.now()).toLocaleDateString();
    document.getElementById('profile-joined').textContent = joinedDate;

    // Update avatar
    this.updateAvatar();

    // Update profile details
    document.getElementById('detail-full-name').textContent = this.profileData?.full_name || 'Not set';
    document.getElementById('detail-username').textContent = this.profileData?.username || 'Not set';
    document.getElementById('detail-website').textContent = this.profileData?.website || 'Not set';

    // Update avatar initials
    const initials = this.getInitials(fullName);
    document.getElementById('avatar-initials').textContent = initials;
  }

  /**
   * Update avatar display
   * Reason for Function: Updates the profile avatar image or placeholder
   * Task Performed: Shows avatar image if available, otherwise shows initials
   * Linking Information:
   *   - Internal Link: Updates avatar elements in profile page
   */
  updateAvatar() {
    const avatarImg = document.getElementById('profile-avatar');
    const avatarPlaceholder = document.getElementById('profile-avatar-placeholder');
    
    if (this.profileData?.avatar_url) {
      avatarImg.src = this.profileData.avatar_url;
      avatarImg.style.display = 'block';
      avatarPlaceholder.style.display = 'none';
    } else {
      avatarImg.style.display = 'none';
      avatarPlaceholder.style.display = 'flex';
    }
  }

  /**
   * Get user initials
   * Reason for Function: Generates initials from user's full name
   * Task Performed: Extracts first letters of first and last name
   * Linking Information:
   *   - Internal Link: Used for avatar placeholder display
   */
  getInitials(name) {
    if (!name || name === 'User') return 'U';
    const names = name.split(' ');
    if (names.length >= 2) {
      return (names[0][0] + names[names.length - 1][0]).toUpperCase();
    }
    return name[0].toUpperCase();
  }

  /**
   * Open edit profile modal
   * Reason for Function: Opens the edit profile modal with current data
   * Task Performed: Shows modal and populates form with current profile data
   * Linking Information:
   *   - Internal Link: Shows modal defined in profile.html
   */
  openEditModal() {
    const modal = document.getElementById('edit-profile-modal');
    if (modal) {
      // Populate form with current data
      document.getElementById('edit-full-name').value = this.profileData?.full_name || '';
      document.getElementById('edit-username').value = this.profileData?.username || '';
      document.getElementById('edit-website').value = this.profileData?.website || '';
      document.getElementById('edit-avatar-url').value = this.profileData?.avatar_url || '';
      
      modal.classList.remove('hidden');
    }
  }

  /**
   * Close edit profile modal
   * Reason for Function: Closes the edit profile modal
   * Task Performed: Hides the modal and resets form
   * Linking Information:
   *   - Internal Link: Hides modal defined in profile.html
   */
  closeEditModal() {
    const modal = document.getElementById('edit-profile-modal');
    if (modal) {
      modal.classList.add('hidden');
      document.getElementById('edit-profile-form').reset();
    }
  }

  /**
   * Handle profile update
   * Reason for Function: Processes profile update form submission
   * Task Performed: Updates profile data in database and refreshes display
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async handleProfileUpdate(e) {
    e.preventDefault();
    
    try {
      const formData = {
        full_name: document.getElementById('edit-full-name').value.trim(),
        username: document.getElementById('edit-username').value.trim(),
        website: document.getElementById('edit-website').value.trim(),
        avatar_url: document.getElementById('edit-avatar-url').value.trim(),
        updated_at: new Date().toISOString()
      };

      const client = window.getSupabaseClient();
      if (!client || !this.currentUser) {
        throw new Error('Database client or user not available');
      }

      // Update profile
      const { error } = await client
        .from('profiles')
        .upsert([{ id: this.currentUser.id, ...formData }]);

      if (error) throw error;

      // Update local data
      this.profileData = { ...this.profileData, ...formData };
      
      // Refresh display
      this.updateProfileDisplay();
      
      // Close modal
      this.closeEditModal();
      
      console.log('Profile updated successfully');

    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please try again.');
    }
  }
}

// Export for global use
window.ProfileManager = ProfileManager;
