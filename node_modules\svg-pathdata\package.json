{"name": "svg-pathdata", "version": "6.0.3", "description": "Manipulate SVG path data (path[d] attribute content) simply and efficiently.", "main": "lib/SVGPathData.cjs", "module": "lib/SVGPathData.module.js", "exports": {"import": "./lib/SVGPathData.module.js", "require": "./lib/SVGPathData.cjs"}, "type": "module", "types": "lib/SVGPathData.d.ts", "scripts": {"changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "cli": "env NODE_ENV=${NODE_ENV:-cli}", "cover": "istanbul cover _mocha --report html -- tests/*.mocha.js -R spec -t 5000", "coveralls": "istanbul cover _mocha --report lcovonly -- tests/*.mocha.js -R spec -t 5000 && cat ./coverage/lcov.info | coveralls && rm -rf ./coverage", "cz": "env NODE_ENV=${NODE_ENV:-cli} git cz", "lint": "eslint tests/*.mocha.js && tslint -p tsconfig.json", "preversion": "npm run test:ci", "test": "mocha tests/*.mocha.js", "version": "npm run changelog && git add CHANGELOG.md", "test:ci": "npm run lint && npm run build && npm run test", "build": "rollup -c rollup.config.js", "watch": "rollup -c rollup.config.js -w"}, "repository": {"type": "git", "url": "https://github.com/nfroidure/svg-pathdata.git"}, "keywords": ["svg", "path", "data", "parser", "encoder", "transformer", "reader", "writer", "stream", "typescript"], "author": "<PERSON>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/nfroidure/SVGPathData/issues"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"@rollup/plugin-typescript": "^8.2.1", "chai": "^4.3.4", "chai-stats": "^0.3.0", "commitizen": "^4.2.3", "conventional-changelog-cli": "^2.1.1", "coveralls": "^3.1.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.23.0", "eslint-config-simplifield": "^12.0.0", "istanbul": "0.4.5", "mocha": "^9.0.2", "mocha-lcov-reporter": "1.3.0", "rollup": "^2.44.0", "rollup-plugin-terser": "^7.0.2", "tslib": "2.1.0", "tslint": "^6.1.3", "typescript": "^4.2.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "greenkeeper": {"ignore": ["debug", "eslint", "eslint-config-simplifield", "mocha", "mocha-lcov-reporter", "commitizen", "cz-conventional-changelog", "coveralls", "istanbul", "conventional-changelog-cli"]}}