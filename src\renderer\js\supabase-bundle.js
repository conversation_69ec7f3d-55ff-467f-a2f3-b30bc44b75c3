/**
 * Supabase bundle for Electron renderer process
 * Reason for Function: Provides a bundled Supabase client that works in Electron environment
 * Task Performed: Imports and exposes Supabase client for use in renderer process
 * Linking Information:
 *   - External Link: Uses @supabase/supabase-js npm package
 *   - Internal Link: Used by supabase-client.js for client initialization
 */

// Import Supabase using require (Node.js style for Electron)
const { createClient } = require('@supabase/supabase-js');

// Make Supabase available globally
window.supabaseCreateClient = createClient;

console.log('Supabase bundle loaded successfully');
