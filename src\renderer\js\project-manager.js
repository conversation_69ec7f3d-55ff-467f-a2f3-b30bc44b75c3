/**
 * Project management functionality
 * Reason for Function: Handles project CRUD operations, progress tracking, and project display
 * Task Performed: Manages project creation, editing, deletion, and progress updates
 * Linking Information:
 *   - Internal Link: Uses Supabase MCP tools for database operations
 *   - Internal Link: Interacts with project elements in src/renderer/pages/projects.html
 *   - Internal Link: Updates project cards and manages project data
 */

class ProjectManager {
  constructor() {
    this.projects = [];
    this.clients = [];
    this.currentEditingProject = null;
    this.stageCounter = 1;
    this.init();
  }

  /**
   * Initialize project manager
   * Reason for Function: Sets up event listeners and loads existing projects and clients
   * Task Performed: Binds modal events, form handlers, and loads project data
   * Linking Information:
   *   - Internal Link: Called when projects page is loaded
   */
  init() {
    this.bindEvents();
    this.loadClients();
    this.loadProjects();
    this.setDefaultDates();
  }

  /**
   * Bind project-related events
   * Reason for Function: Attaches event listeners to project management elements
   * Task Performed: Sets up modal, form, and project interaction handlers
   * Linking Information:
   *   - Internal Link: Binds to elements in projects.html
   */
  bindEvents() {
    // Add project button
    const addProjectBtn = document.getElementById('add-project-btn');
    if (addProjectBtn) {
      addProjectBtn.addEventListener('click', () => this.openProjectModal());
    }

    // Modal close buttons
    const closeModal = document.getElementById('close-project-modal');
    const cancelProject = document.getElementById('cancel-project');
    const modalOverlay = document.querySelector('#project-modal .modal-overlay');

    if (closeModal) {
      closeModal.addEventListener('click', () => this.closeProjectModal());
    }
    if (cancelProject) {
      cancelProject.addEventListener('click', () => this.closeProjectModal());
    }
    if (modalOverlay) {
      modalOverlay.addEventListener('click', () => this.closeProjectModal());
    }

    // Form submission
    const projectForm = document.getElementById('project-form');
    if (projectForm) {
      projectForm.addEventListener('submit', (e) => this.handleProjectSubmission(e));
    }

    // Add stage button
    const addStageBtn = document.getElementById('add-stage');
    if (addStageBtn) {
      addStageBtn.addEventListener('click', () => this.addStageField());
    }

    // Delete modal events
    const closeDeleteModal = document.getElementById('close-delete-project-modal');
    const cancelDelete = document.getElementById('cancel-delete-project');
    const confirmDelete = document.getElementById('confirm-delete-project');
    const deleteModalOverlay = document.querySelector('#delete-project-modal .modal-overlay');

    if (closeDeleteModal) {
      closeDeleteModal.addEventListener('click', () => this.closeDeleteModal());
    }
    if (cancelDelete) {
      cancelDelete.addEventListener('click', () => this.closeDeleteModal());
    }
    if (deleteModalOverlay) {
      deleteModalOverlay.addEventListener('click', () => this.closeDeleteModal());
    }
    if (confirmDelete) {
      confirmDelete.addEventListener('click', () => this.confirmDeleteProject());
    }

    // Auto-calculate remaining amount
    const totalCostInput = document.getElementById('total-cost');
    const advancePaymentInput = document.getElementById('advance-payment');
    
    if (totalCostInput && advancePaymentInput) {
      [totalCostInput, advancePaymentInput].forEach(input => {
        input.addEventListener('input', () => this.calculateRemainingAmount());
      });
    }
  }

  /**
   * Set default dates
   * Reason for Function: Sets default start date to today and due date to 30 days from now
   * Task Performed: Sets default values for date input fields
   * Linking Information:
   *   - Internal Link: Called during initialization
   */
  setDefaultDates() {
    const startDateInput = document.getElementById('start-date');
    const dueDateInput = document.getElementById('due-date');
    
    if (startDateInput && dueDateInput) {
      const today = new Date();
      const futureDate = new Date(today);
      futureDate.setDate(today.getDate() + 30);
      
      startDateInput.value = today.toISOString().split('T')[0];
      dueDateInput.value = futureDate.toISOString().split('T')[0];
    }
  }

  /**
   * Load clients for dropdown
   * Reason for Function: Fetches clients from database to populate client selection dropdown
   * Task Performed: Retrieves clients and populates select options
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadClients() {
    try {
      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();
      
      if (!currentUser) return;

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { data, error } = await client
        .from('clients')
        .select('id, client_name, business_name')
        .eq('user_id', currentUser.id)
        .order('client_name', { ascending: true });

      if (error) throw error;
      
      this.clients = data || [];
      this.populateClientDropdown();
      
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  }

  /**
   * Populate client dropdown
   * Reason for Function: Fills client selection dropdown with available clients
   * Task Performed: Creates option elements for each client
   * Linking Information:
   *   - Internal Link: Called after loading clients from database
   */
  populateClientDropdown() {
    const clientSelect = document.getElementById('client-select');
    if (!clientSelect) return;

    // Clear existing options except the first one
    clientSelect.innerHTML = '<option value="">Select a client</option>';

    this.clients.forEach(client => {
      const option = document.createElement('option');
      option.value = client.id;
      option.textContent = `${client.client_name} (${client.business_name})`;
      clientSelect.appendChild(option);
    });
  }

  /**
   * Calculate remaining amount
   * Reason for Function: Automatically calculates remaining amount based on total cost and advance payment
   * Task Performed: Updates remaining amount field when total cost or advance payment changes
   * Linking Information:
   *   - Internal Link: Called by input event listeners on cost fields
   */
  calculateRemainingAmount() {
    const totalCost = parseFloat(document.getElementById('total-cost').value) || 0;
    const advancePayment = parseFloat(document.getElementById('advance-payment').value) || 0;
    const remainingAmount = Math.max(0, totalCost - advancePayment);
    
    // Store for later use in form submission
    this.calculatedRemainingAmount = remainingAmount;
  }

  /**
   * Open project modal for adding or editing
   * Reason for Function: Shows the project creation/editing modal
   * Task Performed: Displays modal, sets title, clears or populates form
   * Linking Information:
   *   - Internal Link: Called by add project button or edit project action
   */
  openProjectModal(project = null) {
    const modal = document.getElementById('project-modal');
    const title = document.getElementById('project-modal-title');
    const saveBtn = document.getElementById('save-project');
    
    if (modal && title && saveBtn) {
      this.currentEditingProject = project;
      
      if (project) {
        title.textContent = 'Edit Project';
        saveBtn.textContent = 'Update Project';
        this.populateProjectForm(project);
      } else {
        title.textContent = 'Add New Project';
        saveBtn.textContent = 'Save Project';
        this.resetProjectForm();
      }
      
      modal.classList.remove('hidden');
      
      // Focus on first input
      const firstInput = document.getElementById('project-name');
      if (firstInput) {
        setTimeout(() => firstInput.focus(), 100);
      }
    }
  }

  /**
   * Close project modal
   * Reason for Function: Hides the project creation/editing modal
   * Task Performed: Adds hidden class to modal and resets state
   * Linking Information:
   *   - Internal Link: Called by close buttons and overlay click
   */
  closeProjectModal() {
    const modal = document.getElementById('project-modal');
    if (modal) {
      modal.classList.add('hidden');
      this.currentEditingProject = null;
    }
  }

  /**
   * Reset project form
   * Reason for Function: Clears all form fields and resets to default state
   * Task Performed: Clears inputs, resets stages, sets default dates
   * Linking Information:
   *   - Internal Link: Called when opening modal for new project
   */
  resetProjectForm() {
    const form = document.getElementById('project-form');
    if (form) {
      form.reset();
      this.setDefaultDates();
      this.resetStages();
      this.calculatedRemainingAmount = 0;
    }
  }

  /**
   * Reset stages to single field
   * Reason for Function: Resets project stages to default single stage input
   * Task Performed: Clears stage container and adds single stage field
   * Linking Information:
   *   - Internal Link: Called when resetting form
   */
  resetStages() {
    const container = document.getElementById('project-stages-container');
    if (container) {
      container.innerHTML = '';
      this.stageCounter = 0;
      this.addStageField(); // Add one default stage with sub-task support
    }
  }

  /**
   * Add stage field
   * Reason for Function: Adds a new stage input field with sub-task support to the form
   * Task Performed: Creates new stage input with sub-task management and remove button
   * Linking Information:
   *   - Internal Link: Called by add stage button
   */
  addStageField() {
    const container = document.getElementById('project-stages-container');
    if (container) {
      this.stageCounter++;
      const stageItem = document.createElement('div');
      stageItem.className = 'stage-item-form';
      stageItem.dataset.stageIndex = this.stageCounter;
      stageItem.innerHTML = `
        <div class="stage-header-form">
          <input type="text" class="stage-input" placeholder="Enter stage/milestone" ${this.stageCounter === 1 ? 'required' : ''}>
          <button type="button" class="btn btn-sm btn-secondary add-subtask-btn" onclick="projectManager.addSubTaskField(${this.stageCounter})">
            ➕ Add Sub-task
          </button>
          <button type="button" class="remove-stage" ${this.stageCounter === 1 ? 'disabled' : ''}>🗑️</button>
        </div>
        <div class="subtasks-container" id="subtasks-${this.stageCounter}">
          <!-- Sub-tasks will be added here -->
        </div>
      `;
      container.appendChild(stageItem);
      this.bindStageEvents();
    }
  }

  /**
   * Add sub-task field to stage
   * Reason for Function: Adds a sub-task input field to a specific stage during project creation
   * Task Performed: Creates new sub-task input within a stage
   * Linking Information:
   *   - Internal Link: Called by add sub-task button in stage
   */
  addSubTaskField(stageIndex) {
    const container = document.getElementById(`subtasks-${stageIndex}`);
    if (container) {
      const subTaskIndex = container.children.length + 1;
      const subTaskItem = document.createElement('div');
      subTaskItem.className = 'subtask-item-form';
      subTaskItem.innerHTML = `
        <input type="text" class="subtask-input" placeholder="Enter sub-task" data-stage="${stageIndex}">
        <button type="button" class="remove-subtask" onclick="this.parentElement.remove()">🗑️</button>
      `;
      container.appendChild(subTaskItem);
    }
  }

  /**
   * Bind stage events
   * Reason for Function: Attaches event listeners to stage remove buttons
   * Task Performed: Sets up remove functionality for stage fields
   * Linking Information:
   *   - Internal Link: Called after adding stage fields
   */
  bindStageEvents() {
    const removeButtons = document.querySelectorAll('.remove-stage');
    removeButtons.forEach((btn, index) => {
      btn.onclick = () => {
        if (index === 0) return; // Can't remove first stage
        btn.parentElement.remove();
        this.updateRemoveButtonStates();
      };
    });
    this.updateRemoveButtonStates();
  }

  /**
   * Update remove button states
   * Reason for Function: Enables/disables remove buttons based on stage count
   * Task Performed: Disables first remove button, enables others
   * Linking Information:
   *   - Internal Link: Called after stage modifications
   */
  updateRemoveButtonStates() {
    const removeButtons = document.querySelectorAll('.remove-stage');
    removeButtons.forEach((btn, index) => {
      btn.disabled = index === 0;
    });
  }

  /**
   * Populate project form with existing data
   * Reason for Function: Fills form fields with existing project data for editing
   * Task Performed: Sets form field values from project object
   * Linking Information:
   *   - Internal Link: Called when editing existing project
   */
  async populateProjectForm(project) {
    document.getElementById('project-name').value = project.project_name || '';
    document.getElementById('client-select').value = project.client_id || '';
    document.getElementById('project-description').value = project.project_description || '';
    document.getElementById('start-date').value = project.start_date || '';
    document.getElementById('due-date').value = project.due_date || '';
    document.getElementById('total-cost').value = project.total_project_cost || '';
    document.getElementById('advance-payment').value = project.advance_payment || '';

    // Load project stages
    await this.loadProjectStages(project.id);
  }

  /**
   * Load project stages
   * Reason for Function: Loads stages for a specific project when editing
   * Task Performed: Fetches stages from database and populates stage fields
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadProjectStages(projectId) {
    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { data, error } = await client
        .from('project_stages')
        .select('*')
        .eq('project_id', projectId)
        .order('position', { ascending: true });

      if (error) throw error;

      const container = document.getElementById('project-stages-container');
      if (container && data && data.length > 0) {
        container.innerHTML = '';
        
        data.forEach((stage, index) => {
          const stageItem = document.createElement('div');
          stageItem.className = 'stage-item';
          stageItem.innerHTML = `
            <input type="text" class="stage-input" value="${stage.stage_name}" ${index === 0 ? 'required' : ''}>
            <button type="button" class="remove-stage" ${index === 0 ? 'disabled' : ''}>🗑️</button>
          `;
          container.appendChild(stageItem);
        });
        
        this.bindStageEvents();
      }
      
    } catch (error) {
      console.error('Error loading project stages:', error);
    }
  }

  /**
   * Handle project form submission
   * Reason for Function: Processes project creation/editing form and saves to database
   * Task Performed: Validates form, creates/updates project object, saves to database
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async handleProjectSubmission(event) {
    event.preventDefault();
    
    // Get form data
    const projectData = {
      project_name: document.getElementById('project-name').value.trim(),
      client_id: document.getElementById('client-select').value,
      project_description: document.getElementById('project-description').value.trim(),
      start_date: document.getElementById('start-date').value,
      due_date: document.getElementById('due-date').value,
      total_project_cost: parseFloat(document.getElementById('total-cost').value) || 0,
      advance_payment: parseFloat(document.getElementById('advance-payment').value) || 0
    };

    // Calculate remaining amount
    projectData.remaining_amount = Math.max(0, projectData.total_project_cost - projectData.advance_payment);

    // Get stages with sub-tasks
    const stageItems = document.querySelectorAll('.stage-item-form');
    const stagesWithSubTasks = [];

    stageItems.forEach((stageItem, index) => {
      const stageInput = stageItem.querySelector('.stage-input');
      const stageName = stageInput ? stageInput.value.trim() : '';

      if (stageName) {
        const stageData = {
          name: stageName,
          subTasks: []
        };

        // Get sub-tasks for this stage
        const subTaskInputs = stageItem.querySelectorAll('.subtask-input');
        subTaskInputs.forEach(subTaskInput => {
          const subTaskName = subTaskInput.value.trim();
          if (subTaskName) {
            stageData.subTasks.push(subTaskName);
          }
        });

        stagesWithSubTasks.push(stageData);
      }
    });

    // For backward compatibility, also create simple stages array
    const stages = stagesWithSubTasks.map(stage => stage.name);

    // Validation
    if (!projectData.project_name || !projectData.client_id || !projectData.start_date || 
        !projectData.due_date || projectData.total_project_cost <= 0 || stages.length === 0) {
      alert('Please fill in all required fields and add at least one project stage.');
      return;
    }

    // Date validation
    if (new Date(projectData.start_date) > new Date(projectData.due_date)) {
      alert('Due date must be after start date.');
      return;
    }

    try {
      const saveBtn = document.getElementById('save-project');
      this.setLoading(saveBtn, true);

      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      projectData.user_id = currentUser.id;

      if (this.currentEditingProject) {
        await this.updateProject(this.currentEditingProject.id, projectData, stagesWithSubTasks);
      } else {
        await this.createProject(projectData, stagesWithSubTasks);
      }

      this.closeProjectModal();
      this.loadProjects();
      
    } catch (error) {
      console.error('Error saving project:', error);
      alert('Failed to save project. Please try again.');
    } finally {
      const saveBtn = document.getElementById('save-project');
      this.setLoading(saveBtn, false);
    }
  }

  /**
   * Create project in database using MCP tools
   * Reason for Function: Saves new project data to Supabase database with stages and sub-tasks
   * Task Performed: Inserts new project record and creates associated stages with sub-tasks
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async createProject(projectData, stagesWithSubTasks) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    // Create main project
    const { data: projectResult, error: projectError } = await client
      .from('projects')
      .insert([projectData])
      .select()
      .single();

    if (projectError) throw projectError;

    // Create project stages with sub-tasks
    if (stagesWithSubTasks && stagesWithSubTasks.length > 0) {
      for (let i = 0; i < stagesWithSubTasks.length; i++) {
        const stageData = stagesWithSubTasks[i];

        // Create the stage
        const { data: stageResult, error: stageError } = await client
          .from('project_stages')
          .insert([{
            stage_name: stageData.name,
            stage_description: null,
            is_completed: false,
            position: i,
            project_id: projectResult.id,
            user_id: projectData.user_id
          }])
          .select()
          .single();

        if (stageError) {
          console.error('Error creating project stage:', stageError);
          continue;
        }

        // Create sub-tasks for this stage
        if (stageData.subTasks && stageData.subTasks.length > 0) {
          const subTasksData = stageData.subTasks.map((subTaskName, subIndex) => ({
            title: subTaskName,
            description: null,
            is_completed: false,
            position: subIndex,
            stage_id: stageResult.id,
            user_id: projectData.user_id
          }));

          const { error: subTasksError } = await client
            .from('stage_sub_tasks')
            .insert(subTasksData);

          if (subTasksError) {
            console.error('Error creating stage sub-tasks:', subTasksError);
          }
        }
      }
    }

    // Create payment records for the project
    await this.createProjectPayments(projectResult, projectData);

    return projectResult;
  }

  /**
   * Create payment records for a new project
   * Reason for Function: Automatically creates payment records when a project is created
   * Task Performed: Creates advance payment record if advance payment is specified
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   *   - Internal Link: Called by createProject to populate payments table
   */
  async createProjectPayments(projectResult, projectData) {
    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Create advance payment record if advance payment is specified
      if (projectData.advance_payment && parseFloat(projectData.advance_payment) > 0) {
        const paymentData = {
          project_id: projectResult.id,
          payment_type: 'advance',
          amount: parseFloat(projectData.advance_payment),
          payment_date: new Date().toISOString().split('T')[0], // Today's date
          payment_method: 'bank_transfer', // Default payment method
          notes: `Advance payment for project: ${projectData.project_name}`,
          is_confirmed: true, // Assume advance payments are confirmed
          user_id: projectData.user_id
        };

        const { error: paymentError } = await client
          .from('payments')
          .insert([paymentData]);

        if (paymentError) {
          console.error('Error creating payment record:', paymentError);
        } else {
          console.log('Payment record created successfully for project:', projectData.project_name);
        }
      }

      // If there's remaining amount, we could create a pending payment record
      if (projectData.remaining_amount && parseFloat(projectData.remaining_amount) > 0) {
        const remainingPaymentData = {
          project_id: projectResult.id,
          payment_type: 'remaining',
          amount: parseFloat(projectData.remaining_amount),
          payment_date: projectData.due_date || new Date().toISOString().split('T')[0],
          payment_method: 'bank_transfer',
          notes: `Remaining payment for project: ${projectData.project_name}`,
          is_confirmed: false, // Remaining payments are not confirmed yet
          user_id: projectData.user_id
        };

        const { error: remainingPaymentError } = await client
          .from('payments')
          .insert([remainingPaymentData]);

        if (remainingPaymentError) {
          console.error('Error creating remaining payment record:', remainingPaymentError);
        } else {
          console.log('Remaining payment record created for project:', projectData.project_name);
        }
      }

    } catch (error) {
      console.error('Error creating project payments:', error);
    }
  }

  /**
   * Update project in database using MCP tools
   * Reason for Function: Updates existing project data in Supabase database with stages and sub-tasks
   * Task Performed: Updates project record and manages stages with sub-tasks
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async updateProject(projectId, projectData, stagesWithSubTasks) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    // Update main project
    const { data: projectResult, error: projectError } = await client
      .from('projects')
      .update({
        ...projectData,
        updated_at: new Date().toISOString()
      })
      .eq('id', projectId)
      .select()
      .single();

    if (projectError) throw projectError;

    // Delete existing stages (this will cascade delete sub-tasks)
    await client
      .from('project_stages')
      .delete()
      .eq('project_id', projectId);

    // Create new stages with sub-tasks
    if (stagesWithSubTasks && stagesWithSubTasks.length > 0) {
      for (let i = 0; i < stagesWithSubTasks.length; i++) {
        const stageData = stagesWithSubTasks[i];

        // Create the stage
        const { data: stageResult, error: stageError } = await client
          .from('project_stages')
          .insert([{
            stage_name: stageData.name,
            stage_description: null,
            is_completed: false,
            position: i,
            project_id: projectId,
            user_id: projectData.user_id
          }])
          .select()
          .single();

        if (stageError) {
          console.error('Error updating project stage:', stageError);
          continue;
        }

        // Create sub-tasks for this stage
        if (stageData.subTasks && stageData.subTasks.length > 0) {
          const subTasksData = stageData.subTasks.map((subTaskName, subIndex) => ({
            title: subTaskName,
            description: null,
            is_completed: false,
            position: subIndex,
            stage_id: stageResult.id,
            user_id: projectData.user_id
          }));

          const { error: subTasksError } = await client
            .from('stage_sub_tasks')
            .insert(subTasksData);

          if (subTasksError) {
            console.error('Error updating stage sub-tasks:', subTasksError);
          }
        }
      }
    }

    return projectResult;
  }

  /**
   * Load projects from database using MCP tools
   * Reason for Function: Retrieves and displays all user projects with client information, stages, and sub-tasks
   * Task Performed: Fetches projects from database with related stages and sub-tasks, then updates display
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   *   - Internal Link: Loads project stages and stage sub-tasks for display in project cards
   */
  async loadProjects() {
    try {
      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();

      if (!currentUser) return;

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Load projects with client information
      const { data: projectsData, error: projectsError } = await client
        .from('projects')
        .select(`
          *,
          clients (
            client_name,
            business_name
          )
        `)
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (projectsError) throw projectsError;

      this.projects = projectsData || [];

      // Load stages and sub-tasks for each project
      for (const project of this.projects) {
        await this.loadProjectStagesWithSubTasks(project);
      }

      this.renderProjectsGrid();

    } catch (error) {
      console.error('Error loading projects:', error);
    }
  }

  /**
   * Load project stages with sub-tasks
   * Reason for Function: Loads stages and their associated sub-tasks for a specific project
   * Task Performed: Fetches stages and sub-tasks from database and attaches to project object
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   *   - Internal Link: Called by loadProjects to enrich project data
   */
  async loadProjectStagesWithSubTasks(project) {
    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Load project stages
      const { data: stagesData, error: stagesError } = await client
        .from('project_stages')
        .select('*')
        .eq('project_id', project.id)
        .order('position', { ascending: true });

      if (stagesError) throw stagesError;

      project.stages = stagesData || [];

      // Load sub-tasks for each stage
      for (const stage of project.stages) {
        const { data: subTasksData, error: subTasksError } = await client
          .from('stage_sub_tasks')
          .select('*')
          .eq('stage_id', stage.id)
          .order('position', { ascending: true });

        if (subTasksError) throw subTasksError;

        stage.subTasks = subTasksData || [];
      }

    } catch (error) {
      console.error('Error loading project stages with sub-tasks:', error);
      project.stages = [];
    }
  }

  /**
   * Render projects grid
   * Reason for Function: Displays projects as cards with progress bars
   * Task Performed: Creates project cards with all project information
   * Linking Information:
   *   - Internal Link: Updates grid elements in projects.html
   */
  renderProjectsGrid() {
    const projectsGrid = document.getElementById('projects-grid');
    const emptyState = document.getElementById('projects-empty-state');

    if (!projectsGrid || !emptyState) return;

    if (this.projects.length === 0) {
      projectsGrid.style.display = 'none';
      emptyState.classList.remove('hidden');
      return;
    }

    projectsGrid.style.display = 'grid';
    emptyState.classList.add('hidden');

    projectsGrid.innerHTML = '';

    this.projects.forEach(project => {
      const projectCard = this.createProjectCard(project);
      projectsGrid.appendChild(projectCard);
    });
  }

  /**
   * Create project card element
   * Reason for Function: Generates HTML element for individual project display with stages and sub-tasks
   * Task Performed: Creates project card with progress bar, project details, stages, and sub-tasks
   * Linking Information:
   *   - Internal Link: Used by renderProjectsGrid to create project displays
   *   - Internal Link: Displays stages and sub-tasks loaded by loadProjectStagesWithSubTasks
   */
  createProjectCard(project) {
    const card = document.createElement('div');
    card.className = 'project-card';
    card.dataset.projectId = project.id;

    const clientName = project.clients ? project.clients.client_name : 'No Client';
    const businessName = project.clients ? project.clients.business_name : '';
    const startDate = new Date(project.start_date).toLocaleDateString();
    const dueDate = new Date(project.due_date).toLocaleDateString();
    const progress = project.progress_percentage || 0;
    const status = project.status || 'not_started';
    const paymentStatus = project.payment_status || 'pending';

    // Generate stages HTML
    const stagesHtml = this.generateStagesHtml(project.stages || []);

    card.innerHTML = `
      <div class="project-card-header">
        <h3 class="project-title">${project.project_name}</h3>
        <div class="project-actions">
          <button class="btn-icon edit-btn" onclick="projectManager.editProject('${project.id}')" title="Edit Project">
            ✏️
          </button>
          <button class="btn-icon delete-btn" onclick="projectManager.deleteProject('${project.id}', '${project.project_name}')" title="Delete Project">
            🗑️
          </button>
        </div>
      </div>

      <div class="project-info">
        <div class="project-client">
          <strong>Client:</strong> ${clientName}${businessName ? ` (${businessName})` : ''}
        </div>
        <div class="project-dates">
          <span><strong>Start:</strong> ${startDate}</span>
          <span><strong>Due:</strong> ${dueDate}</span>
        </div>
        <div class="project-financial">
          <span><strong>Total:</strong> $${project.total_project_cost.toFixed(2)}</span>
          <span><strong>Advance:</strong> $${project.advance_payment.toFixed(2)}</span>
          <span><strong>Remaining:</strong> $${project.remaining_amount.toFixed(2)}</span>
        </div>
      </div>

      ${stagesHtml}

      <div class="project-progress">
        <div class="progress-header">
          <span>Progress</span>
          <span class="progress-percentage">${progress}%</span>
        </div>
        <div class="progress-bar-container">
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${progress}%"></div>
          </div>
          <input type="range" class="progress-slider" min="0" max="100" step="5" value="${progress}"
                 onchange="projectManager.updateProgress('${project.id}', this.value)">
        </div>
      </div>

      <div class="project-status">
        <span class="status-badge status-${status}">${this.getStatusLabel(status)}</span>
        <span class="status-badge payment-${paymentStatus}">${this.getPaymentStatusLabel(paymentStatus)}</span>
      </div>
    `;

    return card;
  }

  /**
   * Generate stages HTML for project card
   * Reason for Function: Creates HTML structure for displaying project stages and their sub-tasks
   * Task Performed: Generates collapsible stages section with sub-tasks and completion status
   * Linking Information:
   *   - Internal Link: Used by createProjectCard to display stages and sub-tasks
   */
  generateStagesHtml(stages) {
    if (!stages || stages.length === 0) {
      return '<div class="project-stages"><p class="no-stages">No stages defined</p></div>';
    }

    const stagesHtml = stages.map(stage => {
      const completedSubTasks = stage.subTasks ? stage.subTasks.filter(st => st.is_completed).length : 0;
      const totalSubTasks = stage.subTasks ? stage.subTasks.length : 0;
      const stageProgress = totalSubTasks > 0 ? Math.round((completedSubTasks / totalSubTasks) * 100) : 0;

      const subTasksHtml = stage.subTasks && stage.subTasks.length > 0
        ? stage.subTasks.map(subTask => `
            <div class="sub-task ${subTask.is_completed ? 'completed' : ''}">
              <input type="checkbox" ${subTask.is_completed ? 'checked' : ''}
                     onchange="projectManager.toggleSubTask('${subTask.id}', this.checked)">
              <span class="sub-task-title">${subTask.title}</span>
              ${subTask.description ? `<span class="sub-task-desc">${subTask.description}</span>` : ''}
            </div>
          `).join('')
        : '<div class="no-sub-tasks">No sub-tasks</div>';

      return `
        <div class="stage-item ${stage.is_completed ? 'completed' : ''}">
          <div class="stage-header" onclick="projectManager.toggleStageExpansion('${stage.id}')">
            <div class="stage-info">
              <input type="checkbox" ${stage.is_completed ? 'checked' : ''}
                     onchange="projectManager.toggleStage('${stage.id}', this.checked)"
                     onclick="event.stopPropagation()">
              <span class="stage-name">${stage.stage_name}</span>
              <span class="stage-progress">${completedSubTasks}/${totalSubTasks} tasks (${stageProgress}%)</span>
            </div>
            <span class="expand-icon">▼</span>
          </div>
          <div class="stage-content collapsed" id="stage-content-${stage.id}">
            <div class="sub-tasks-container">
              ${subTasksHtml}
              <button class="btn btn-sm add-sub-task-btn" onclick="projectManager.addSubTask('${stage.id}')">
                ➕ Add Sub-task
              </button>
            </div>
          </div>
        </div>
      `;
    }).join('');

    return `
      <div class="project-stages">
        <div class="stages-header">
          <h4>Project Stages</h4>
          <span class="stages-count">${stages.length} stages</span>
        </div>
        <div class="stages-list">
          ${stagesHtml}
        </div>
      </div>
    `;
  }

  /**
   * Get status label
   * Reason for Function: Converts status code to human-readable label
   * Task Performed: Returns formatted status text
   * Linking Information:
   *   - Internal Link: Used by createProjectCard for status display
   */
  getStatusLabel(status) {
    switch (status) {
      case 'not_started': return 'Not Started';
      case 'in_progress': return 'In Progress';
      case 'completed': return 'Completed';
      default: return 'Unknown';
    }
  }

  /**
   * Get payment status label
   * Reason for Function: Converts payment status code to human-readable label
   * Task Performed: Returns formatted payment status text
   * Linking Information:
   *   - Internal Link: Used by createProjectCard for payment status display
   */
  getPaymentStatusLabel(paymentStatus) {
    switch (paymentStatus) {
      case 'pending': return 'Payment Pending';
      case 'partial': return 'Partially Paid';
      case 'full_paid': return 'Fully Paid';
      default: return 'Unknown';
    }
  }

  /**
   * Update project progress
   * Reason for Function: Updates project progress percentage and status
   * Task Performed: Updates progress in database and refreshes display
   * Linking Information:
   *   - Internal Link: Called by progress slider change events
   */
  async updateProgress(projectId, newProgress) {
    try {
      const progress = parseInt(newProgress);
      let newStatus = 'not_started';

      if (progress > 0 && progress < 100) {
        newStatus = 'in_progress';
      } else if (progress === 100) {
        newStatus = 'completed';
      }

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { error } = await client
        .from('projects')
        .update({
          progress_percentage: progress,
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);

      if (error) throw error;

      // Update the display
      this.loadProjects();

    } catch (error) {
      console.error('Error updating project progress:', error);
      alert('Failed to update project progress. Please try again.');
    }
  }

  /**
   * Edit project
   * Reason for Function: Opens modal with project data for editing
   * Task Performed: Finds project by ID and opens edit modal
   * Linking Information:
   *   - Internal Link: Called by edit button in project card
   */
  editProject(projectId) {
    const project = this.projects.find(p => p.id === projectId);
    if (project) {
      this.openProjectModal(project);
    }
  }

  /**
   * Delete project
   * Reason for Function: Shows delete confirmation modal
   * Task Performed: Opens delete confirmation modal with project details
   * Linking Information:
   *   - Internal Link: Called by delete button in project card
   */
  deleteProject(projectId, projectName) {
    this.projectToDelete = projectId;
    const modal = document.getElementById('delete-project-modal');
    const nameElement = document.getElementById('delete-project-name');

    if (modal && nameElement) {
      nameElement.textContent = projectName;
      modal.classList.remove('hidden');
    }
  }

  /**
   * Close delete modal
   * Reason for Function: Hides the delete confirmation modal
   * Task Performed: Adds hidden class to delete modal
   * Linking Information:
   *   - Internal Link: Called by cancel and close buttons
   */
  closeDeleteModal() {
    const modal = document.getElementById('delete-project-modal');
    if (modal) {
      modal.classList.add('hidden');
      this.projectToDelete = null;
    }
  }

  /**
   * Confirm delete project
   * Reason for Function: Deletes project from database after confirmation
   * Task Performed: Removes project from database using MCP tools
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async confirmDeleteProject() {
    if (!this.projectToDelete) return;

    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { error } = await client
        .from('projects')
        .delete()
        .eq('id', this.projectToDelete);

      if (error) throw error;

      this.closeDeleteModal();
      this.loadProjects();

    } catch (error) {
      console.error('Error deleting project:', error);
      alert('Failed to delete project. Please try again.');
    }
  }

  /**
   * Toggle stage expansion
   * Reason for Function: Expands or collapses stage content to show/hide sub-tasks
   * Task Performed: Toggles CSS classes and icon rotation for stage expansion
   * Linking Information:
   *   - Internal Link: Called by stage header click events
   */
  toggleStageExpansion(stageId) {
    const stageContent = document.getElementById(`stage-content-${stageId}`);
    const expandIcon = stageContent?.parentElement.querySelector('.expand-icon');

    if (stageContent) {
      stageContent.classList.toggle('collapsed');
      if (expandIcon) {
        expandIcon.style.transform = stageContent.classList.contains('collapsed') ? 'rotate(0deg)' : 'rotate(180deg)';
      }
    }
  }

  /**
   * Toggle stage completion status
   * Reason for Function: Updates stage completion status in database
   * Task Performed: Updates stage is_completed field and refreshes display
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async toggleStage(stageId, isCompleted) {
    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { error } = await client
        .from('project_stages')
        .update({
          is_completed: isCompleted,
          updated_at: new Date().toISOString()
        })
        .eq('id', stageId);

      if (error) throw error;

      // Refresh the projects display
      this.loadProjects();

    } catch (error) {
      console.error('Error updating stage:', error);
      alert('Failed to update stage. Please try again.');
    }
  }

  /**
   * Toggle sub-task completion status
   * Reason for Function: Updates sub-task completion status in database
   * Task Performed: Updates sub-task is_completed field and refreshes display
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async toggleSubTask(subTaskId, isCompleted) {
    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const { error } = await client
        .from('stage_sub_tasks')
        .update({
          is_completed: isCompleted,
          updated_at: new Date().toISOString()
        })
        .eq('id', subTaskId);

      if (error) throw error;

      // Refresh the projects display
      this.loadProjects();

    } catch (error) {
      console.error('Error updating sub-task:', error);
      alert('Failed to update sub-task. Please try again.');
    }
  }

  /**
   * Add new sub-task to stage
   * Reason for Function: Creates new sub-task for a specific stage
   * Task Performed: Prompts user for sub-task details and creates in database
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async addSubTask(stageId) {
    const title = prompt('Enter sub-task title:');
    if (!title || !title.trim()) return;

    const description = prompt('Enter sub-task description (optional):') || '';

    try {
      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();

      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Get the highest position for this stage
      const { data: existingSubTasks } = await client
        .from('stage_sub_tasks')
        .select('position')
        .eq('stage_id', stageId)
        .order('position', { ascending: false })
        .limit(1);

      const nextPosition = existingSubTasks && existingSubTasks.length > 0
        ? existingSubTasks[0].position + 1
        : 0;

      const { error } = await client
        .from('stage_sub_tasks')
        .insert([{
          title: title.trim(),
          description: description.trim(),
          stage_id: stageId,
          user_id: currentUser.id,
          position: nextPosition
        }]);

      if (error) throw error;

      // Refresh the projects display
      this.loadProjects();

    } catch (error) {
      console.error('Error adding sub-task:', error);
      alert('Failed to add sub-task. Please try again.');
    }
  }

  /**
   * Set loading state for buttons
   * Reason for Function: Shows loading state during async operations
   * Task Performed: Adds/removes loading class and disables button
   * Linking Information:
   *   - Internal Link: Used during project creation and updates
   */
  setLoading(button, loading) {
    if (loading) {
      button.classList.add('loading');
      button.disabled = true;
    } else {
      button.classList.remove('loading');
      button.disabled = false;
    }
  }
}

// Export for global use
window.ProjectManager = ProjectManager;
