/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Anvil = createLucideIcon("Anvil", [
  ["path", { d: "M7 10c-2.8 0-5-2.2-5-5h5", key: "1d6adc" }],
  ["path", { d: "M7 4v8h7a8 8 0 0 0 8-8Z", key: "uu98hv" }],
  ["path", { d: "M9 12v5", key: "3anwtq" }],
  ["path", { d: "M15 12v5", key: "5xh3zn" }],
  ["path", { d: "M5 20a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3v1H5Z", key: "10a9tj" }]
]);

export { Anvil as default };
//# sourceMappingURL=anvil.js.map
