/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const CalendarX = createLucideIcon("CalendarX", [
  ["rect", { width: "18", height: "18", x: "3", y: "4", rx: "2", ry: "2", key: "eu3xkr" }],
  ["line", { x1: "16", x2: "16", y1: "2", y2: "6", key: "m3sa8f" }],
  ["line", { x1: "8", x2: "8", y1: "2", y2: "6", key: "18kwsl" }],
  ["line", { x1: "3", x2: "21", y1: "10", y2: "10", key: "xt86sb" }],
  ["line", { x1: "10", x2: "14", y1: "14", y2: "18", key: "1g3qc0" }],
  ["line", { x1: "14", x2: "10", y1: "14", y2: "18", key: "1az83m" }]
]);

export { CalendarX as default };
//# sourceMappingURL=calendar-x.js.map
