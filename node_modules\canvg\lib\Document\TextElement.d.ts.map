{"version": 3, "file": "TextElement.d.ts", "sourceRoot": "", "sources": ["../../src/Document/TextElement.ts"], "names": [], "mappings": "AAAA,OAAO,EACN,kBAAkB,EAClB,MAAM,UAAU,CAAC;AAQlB,OAAO,WAAW,MAAM,gBAAgB,CAAC;AAEzC,OAAO,QAAQ,MAAM,YAAY,CAAC;AAClC,OAAO,OAAO,MAAM,WAAW,CAAC;AAChC,OAAO,WAAW,MAAM,eAAe,CAAC;AACxC,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAC1C,OAAO,eAAe,MAAM,mBAAmB,CAAC;AAEhD,MAAM,CAAC,OAAO,OAAO,WAAY,SAAQ,eAAe;IACvD,IAAI,SAAU;IACd,SAAS,CAAC,CAAC,SAAK;IAChB,SAAS,CAAC,CAAC,SAAK;IAChB,OAAO,CAAC,SAAS,CAAgB;IACjC,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,IAAI,CAAS;IACrB,OAAO,CAAC,IAAI,CAAS;IACrB,OAAO,CAAC,YAAY,CAAM;gBAGzB,QAAQ,EAAE,QAAQ,EAClB,IAAI,EAAE,WAAW,EACjB,gBAAgB,CAAC,EAAE,OAAO;IAW3B,UAAU,CAAC,GAAG,EAAE,kBAAkB,EAAE,WAAW,UAAQ;IAWvD,SAAS,CAAC,qBAAqB;IAS/B,cAAc,CAAC,GAAG,EAAE,kBAAkB;IAyBtC,SAAS,CAAC,WAAW;IAWrB,SAAS,CAAC,sBAAsB,CAAC,GAAG,EAAE,kBAAkB;IAWxD,QAAQ,CACP,IAAI,EAAE,WAAW,EACjB,IAAI,EAAE,MAAM,EACZ,CAAC,EAAE,MAAM;IA0CV,OAAO;IAIP,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,SAAS;IAuB1C,cAAc,CAAC,GAAG,EAAE,kBAAkB;IA4BtC,SAAS,CAAC,sBAAsB,CAAC,GAAG,EAAE,kBAAkB;IAiFxD,SAAS,CAAC,cAAc;IAgCxB,SAAS,CAAC,+BAA+B,CAAC,GAAG,EAAE,kBAAkB;IAOjE,SAAS,CAAC,mCAAmC,CAC5C,GAAG,EAAE,kBAAkB,EACvB,UAAU,EAAE,WAAW,EACvB,MAAM,EAAE,OAAO,EACf,CAAC,EAAE,MAAM,GACP,IAAI;IAaP,SAAS,CAAC,sBAAsB,CAC/B,GAAG,EAAE,kBAAkB,EACvB,UAAU,EAAE,WAAW,EACvB,MAAM,EAAE,OAAO,EACf,CAAC,EAAE,MAAM;IA8FV,SAAS,CAAC,mBAAmB,CAC5B,GAAG,EAAE,kBAAkB,EACvB,UAAU,EAAE,WAAW,EACvB,MAAM,EAAE,OAAO,EACf,CAAC,EAAE,MAAM;IAwBV,SAAS,CAAC,WAAW,CACpB,GAAG,EAAE,kBAAkB,EACvB,UAAU,EAAE,WAAW,EACvB,MAAM,EAAE,OAAO,EACf,CAAC,EAAE,MAAM;IAUV,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,kBAAkB;IAiB7C,SAAS,CAAC,iBAAiB,CAC1B,GAAG,EAAE,kBAAkB,EACvB,UAAU,EAAE,MAAM;IAoDnB;;;;;OAKG;IACH,SAAS,CAAC,qBAAqB,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;CAgB5D"}