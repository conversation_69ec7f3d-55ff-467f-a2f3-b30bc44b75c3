<!-- Payments Page Content -->
<div class="payments-page">
    <div class="page-header">
        <h2>Payment Management</h2>
        <button class="btn btn-primary" id="add-payment-btn">
            <span class="icon">💰</span>
            Add Payment
        </button>
    </div>

    <!-- Payment Statistics Cards -->
    <div class="payment-stats">
        <div class="stat-card">
            <div class="stat-icon">💰</div>
            <div class="stat-content">
                <h3>Total Payments</h3>
                <p class="stat-number" id="total-payments">$0</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
                <h3>Confirmed Payments</h3>
                <p class="stat-number" id="confirmed-payments">$0</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">⏳</div>
            <div class="stat-content">
                <h3>Pending Payments</h3>
                <p class="stat-number" id="pending-payments">$0</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <h3>Payment Records</h3>
                <p class="stat-number" id="payment-count">0</p>
            </div>
        </div>
    </div>

    <!-- Payment Filters -->
    <div class="payment-filters">
        <div class="filter-group">
            <label for="filter-type">Payment Type:</label>
            <select id="filter-type">
                <option value="">All Types</option>
                <option value="advance">Advance Payment</option>
                <option value="remaining">Remaining Payment</option>
                <option value="full">Full Payment</option>
                <option value="partial">Partial Payment</option>
            </select>
        </div>
        <div class="filter-group">
            <label for="filter-status">Status:</label>
            <select id="filter-status">
                <option value="">All Status</option>
                <option value="confirmed">Confirmed</option>
                <option value="pending">Pending</option>
            </select>
        </div>
        <div class="filter-group">
            <label for="filter-method">Payment Method:</label>
            <select id="filter-method">
                <option value="">All Methods</option>
                <option value="bank_transfer">Bank Transfer</option>
                <option value="cash">Cash</option>
                <option value="check">Check</option>
                <option value="online">Online Payment</option>
            </select>
        </div>
        <button class="btn btn-secondary" id="clear-filters">Clear Filters</button>
    </div>

    <!-- Payment Table -->
    <div class="table-container" id="payments-table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>Project</th>
                    <th>Client</th>
                    <th>Payment Type</th>
                    <th>Amount</th>
                    <th>Payment Date</th>
                    <th>Method</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="payments-table-body">
                <!-- Payment rows will be inserted here -->
            </tbody>
        </table>
    </div>

    <!-- Empty State -->
    <div id="payments-empty-state" class="empty-state hidden">
        <div class="empty-icon">💳</div>
        <h3>No Payment Records Found</h3>
        <p>Payment records will appear here when projects with payments are created.</p>
        <button class="btn btn-primary" id="refresh-payments">
            <span class="icon">🔄</span>
            Refresh
        </button>
    </div>

    <!-- Loading State -->
    <div id="payments-loading" class="loading-state hidden">
        <div class="loading-spinner"></div>
        <p>Loading payment records...</p>
    </div>
</div>

<!-- Add Payment Modal -->
<div id="add-payment-modal" class="modal hidden">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Add New Payment</h3>
            <button class="modal-close" id="close-payment-modal">&times;</button>
        </div>
        <form id="payment-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="payment-project">Project *</label>
                    <select id="payment-project" required>
                        <option value="">Select Project</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="payment-type">Payment Type *</label>
                    <select id="payment-type" required>
                        <option value="">Select Type</option>
                        <option value="advance">Advance Payment</option>
                        <option value="remaining">Remaining Payment</option>
                        <option value="full">Full Payment</option>
                        <option value="partial">Partial Payment</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="payment-amount">Amount *</label>
                    <input type="number" id="payment-amount" step="0.01" min="0" required placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="payment-date">Payment Date *</label>
                    <input type="date" id="payment-date" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="payment-method">Payment Method</label>
                    <select id="payment-method">
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="cash">Cash</option>
                        <option value="check">Check</option>
                        <option value="online">Online Payment</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="payment-confirmed">Status</label>
                    <select id="payment-confirmed">
                        <option value="true">Confirmed</option>
                        <option value="false">Pending</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="payment-notes">Notes</label>
                <textarea id="payment-notes" rows="3" placeholder="Additional notes about this payment"></textarea>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn btn-secondary" id="cancel-payment">Cancel</button>
                <button type="submit" class="btn btn-primary">Add Payment</button>
            </div>
        </form>
    </div>
</div>

<!-- Payment Details Modal -->
<div id="payment-details-modal" class="modal hidden">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Payment Details</h3>
            <button class="modal-close" id="close-details-modal">&times;</button>
        </div>
        <div class="payment-details-content">
            <!-- Payment details will be populated here -->
        </div>
        <div class="modal-actions">
            <button type="button" class="btn btn-secondary" id="close-payment-details">Close</button>
            <button type="button" class="btn btn-primary" id="edit-payment">Edit Payment</button>
        </div>
    </div>
</div>
